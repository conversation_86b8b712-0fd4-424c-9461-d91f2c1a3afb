import { Request, Response, NextFunction } from 'express';
import { ServerAction } from '../../../../types/Server';
import Constants from '../../../../utils/Constants';
import Logging from '../../../../utils/Logging';
import { Action } from '../../../../types/Authorization';
import SmartMeterValidatorRest from '../validator/SmartMeterValidatorRest';

import AssetStorage from '../../../../storage/mongodb/AssetStorage';
import AuthorizationService from './AuthorizationService';
import UtilsService from './UtilsService';
import { TenantComponents } from '../../../../types/Tenant';
import AssetValidatorRest from '../validator/AssetValidatorRest';
import { Entity } from '../../../../types/Authorization'; // Add this import
import AppError from '../../../../exception/AppError';
import LoggingHelper from '../../../../utils/LoggingHelper';
//import HTTPError from '../../../../exception/HTTPError';
import { HTTPError } from '../../../../types/HTTPError';
import Utils from '../../../../utils/Utils';
import Asset from '../../../../types/Asset';
import SiteArea from '../../../../types/SiteArea';
import { AssetDataResult } from '../../../../types/DataResult';
import SmartMeterStorage from '../../../../storage/mongodb/SmartMeterStorage';
const MODULE_NAME = 'SmartMeterService';

export default class SmartMeterService {
  public static async handleCreateSmartMeter(action: ServerAction, req: Request, res: Response, next: NextFunction): Promise<void> {
  
      UtilsService.assertComponentIsActiveFromToken(req.user, TenantComponents.ASSET,
        Action.CREATE, Entity.ASSET, MODULE_NAME, 'handleCreateAsset');
      // Check request is valid
      const filteredAssetRequest = SmartMeterValidatorRest.getInstance().validateCreateSmartMeterReq(req.body);
      // Check authorizations for current action attempt
      await AuthorizationService.checkAndGetAssetAuthorizations(
        req.tenant, req.user, Action.CREATE, {}, filteredAssetRequest);
      // Check Site Area authorization
      let siteArea: SiteArea = null;
      if (Utils.isComponentActiveFromToken(req.user, TenantComponents.ORGANIZATION) && filteredAssetRequest.siteAreaID) {
        siteArea = await UtilsService.checkAndGetSiteAreaAuthorization(
          req.tenant, req.user, filteredAssetRequest.siteAreaID, Action.UPDATE, action, filteredAssetRequest, { withSite: true }, false);
      }
      // Create asset
      const newAsset: Asset = {
        ...filteredAssetRequest,
        companyID: siteArea?.site ? siteArea.site.companyID : null,
        privateKey: filteredAssetRequest.privateKey,
        publicKey: filteredAssetRequest.publicKey,
        url: filteredAssetRequest.url,
        meterName: filteredAssetRequest.meterName,
        siteID: siteArea ? siteArea.siteID : null,
        issuer: true,
        createdBy: { id: req.user.id },
        createdOn: new Date()
      } as Asset;
      // Save
      newAsset.id = await SmartMeterStorage.saveSmartMeterAsset(req.tenant, newAsset);

      // Log
      await Logging.logInfo({
        ...LoggingHelper.getAssetProperties(newAsset),
        tenantID: req.tenant.id,
        user: req.user,
        module: MODULE_NAME, method: 'handleCreateAsset',
        message: `Asset '${newAsset.id}' has been created successfully`,
        action: action,
        detailedMessages: { asset: newAsset }
      });
      res.json(Object.assign({ id: newAsset.id }, Constants.REST_RESPONSE_SUCCESS));
      next();
    }


  public static async handleUpdateSmartMeter(action: ServerAction, req: Request, res: Response, next: NextFunction): Promise<void> {
    // Check if component is active
        UtilsService.assertComponentIsActiveFromToken(req.user, TenantComponents.ASSET,
          Action.UPDATE, Entity.ASSET, MODULE_NAME, 'handleUpdateAsset');
        // Filter
        const filteredRequest = SmartMeterValidatorRest.getInstance().validateSmartMeterUpdateReq(req.body);
        // Check Site Area authorization
        let siteArea: SiteArea = null;
        if (Utils.isComponentActiveFromToken(req.user, TenantComponents.ORGANIZATION) && filteredRequest.siteAreaID) {
          siteArea = await UtilsService.checkAndGetSiteAreaAuthorization(
            req.tenant, req.user, filteredRequest.siteAreaID, Action.UPDATE, action, filteredRequest, { withSite: true }, false);
        }
        // Check and get asset
        const asset = await UtilsService.checkAndGetSmartMeterAssetAuthorization(req.tenant, req.user, filteredRequest.id, Action.UPDATE, action, filteredRequest);
        // Update Asset values and persist
        asset.name = filteredRequest.name;
        asset.companyID = siteArea?.site ? siteArea.site.companyID : null;
        asset.siteID = siteArea ? siteArea.siteID : null;
        asset.siteAreaID = filteredRequest.siteAreaID;
        asset.assetType = filteredRequest.assetType;
        asset.excludeFromSmartCharging = filteredRequest.excludeFromSmartCharging;
        asset.variationThresholdPercent = filteredRequest.variationThresholdPercent;
        asset.fluctuationPercent = filteredRequest.fluctuationPercent;
        asset.staticValueWatt = filteredRequest.staticValueWatt;
        asset.coordinates = filteredRequest.coordinates;
        asset.image = filteredRequest.image;
        asset.dynamicAsset = filteredRequest.dynamicAsset;
        asset.usesPushAPI = filteredRequest.usesPushAPI;
        asset.connectionID = filteredRequest.connectionID;
        asset.meterID = filteredRequest.meterID;
        asset.lastChangedBy = { 'id': req.user.id };
        asset.lastChangedOn = new Date();
        await SmartMeterStorage.saveSmartMeterAsset(req.tenant, asset);
        // Log
        await Logging.logInfo({
          ...LoggingHelper.getAssetProperties(asset),
          tenantID: req.tenant.id,
          user: req.user,
          module: MODULE_NAME, method: 'handleUpdateAsset',
          message: `Asset '${asset.name}' has been updated successfully`,
          action: action,
          detailedMessages: { asset }
        });
        res.json(Constants.REST_RESPONSE_SUCCESS);
        next();
      }


public static async handledeleteSmartMeter(action: ServerAction, req: Request, res: Response, next: NextFunction): Promise<void> {
   // Check if component is active
      UtilsService.assertComponentIsActiveFromToken(req.user, TenantComponents.ASSET,
        Action.DELETE, Entity.ASSET, MODULE_NAME, 'handleDeleteAsset');
      // Filter
      const filteredRequest = SmartMeterValidatorRest .getInstance().validateSmartMeterDeleteReq(req.query);
      // Check and get Asset
      const asset = await UtilsService.checkAndGetSmartMeterAssetAuthorization(req.tenant, req.user, filteredRequest.ID,
        Action.DELETE, action);
      // Delete
      await SmartMeterStorage.deleteAsset(req.tenant, asset.id);
      // Log
      await Logging.logInfo({
        ...LoggingHelper.getAssetProperties(asset),
        tenantID: req.tenant.id,
        user: req.user,
        module: MODULE_NAME, method: 'handleDeleteAsset',
        message: `Asset '${asset.name}' has been deleted successfully`,
        action: action,
        detailedMessages: { asset }
      });
      res.json(Constants.REST_RESPONSE_SUCCESS);
      next();
}


public static async handleGetSmartMeters(action: ServerAction, req: Request, res: Response, next: NextFunction): Promise<void> {
// Check if component is active
    UtilsService.assertComponentIsActiveFromToken(req.user, TenantComponents.ASSET,
      Action.LIST, Entity.ASSET, MODULE_NAME, 'handleGetAssets');
    // Filter
    const filteredRequest =  SmartMeterValidatorRest.getInstance().validateSmartMetersGetReq(req.query);
    // Check dynamic auth
    const authorizations = await AuthorizationService.checkAndGetAssetsAuthorizations(
      req.tenant, req.user, Action.LIST, filteredRequest, false);
    if (!authorizations.authorized) {
      UtilsService.sendEmptyDataResult(res, next);
      return;
    }
     // Add smart meter specific fields to project fields
    if (authorizations.projectFields) {
      authorizations.projectFields.push('meterName', 'url', 'publicKey', 'privateKey');
    }
    // Get the assets
    const assets = await SmartMeterStorage.getsmartMeterAssets(req.tenant,
      {
        search: filteredRequest.Search,
        issuer: filteredRequest.Issuer,
        meterName: filteredRequest.MeterName,
        url: filteredRequest.Url,
        publicKey: filteredRequest.PublicKey,
        privateKey: filteredRequest.PrivateKey,
        meterID: filteredRequest.MeterID,
        siteAreaIDs: (filteredRequest.SiteAreaID ? filteredRequest.SiteAreaID.split('|') : null),
        siteIDs: (filteredRequest.SiteID ? filteredRequest.SiteID.split('|') : null),
        withSiteArea: filteredRequest.WithSiteArea,
        withSite: filteredRequest.WithSite,
        withNoSiteArea: filteredRequest.WithNoSiteArea,
        dynamicOnly: filteredRequest.DynamicOnly,
        ...authorizations.filters
      },
      {
        limit: filteredRequest.Limit,
        skip: filteredRequest.Skip,
        sort: UtilsService.httpSortFieldsToMongoDB(filteredRequest.SortFields),
        onlyRecordCount: filteredRequest.OnlyRecordCount
      },
      authorizations.projectFields
    );
    // Assign projected fields
    if (authorizations.projectFields) {
      assets.projectFields = authorizations.projectFields;
    }
    // Add Auth flags
    if (filteredRequest.WithAuth) {
      await AuthorizationService.addAssetsAuthorizations(req.tenant, req.user, assets as AssetDataResult, authorizations);
    }
    // Add default values for missing fields
    assets.result = assets.result.map(asset => ({
      ...asset,
      meterName: asset.meterName || '',
      url: asset.url || '',
      publicKey: asset.publicKey || '',
      privateKey: asset.privateKey || ''
    }));
    
    res.json(assets);
    next();
  }

public static async handleGetSmartMeter(action: ServerAction, req: Request, res: Response, next: NextFunction): Promise<void> {
   // Check if component is active
      UtilsService.assertComponentIsActiveFromToken(req.user, TenantComponents.ASSET,
        Action.READ, Entity.ASSET, MODULE_NAME, 'handleGetAsset');
      // Filter
      const filteredRequest = SmartMeterValidatorRest.getInstance().validateSmartMeterGetReq(req.query);
      // Check and get Asset
      const asset = await UtilsService.checkAndGetSmartMeterAssetAuthorization(req.tenant, req.user, filteredRequest.ID, Action.READ, action, null,
        { withSiteArea: filteredRequest.WithSiteArea }, true);
      res.json(asset);
      next();

}
}
