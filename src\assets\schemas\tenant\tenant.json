{"$id": "tenant", "type": "object", "definitions": {"id": {"$ref": "common#/definitions/id"}, "name": {"type": "string", "minLength": 1, "maxLength": 100, "sanitize": "mongo"}, "iosPackage": {"type": "string", "sanitize": "mongo"}, "androidPackage": {"type": "string", "sanitize": "mongo"}, "shortname": {"type": "string", "sanitize": "mongo", "minLength": 1, "maxLength": 3}, "redirectWebsiteUrl": {"type": "string", "sanitize": "mongo"}, "taxLevel": {"type": "string", "sanitize": "mongo"}, "taxLevelValue": {"type": "string", "sanitize": "mongo"}, "email": {"$ref": "common#/definitions/email"}, "subdomain": {"$ref": "common#/definitions/subdomain"}, "components": {"$ref": "tenant-components#/definitions/components"}, "address": {"$ref": "common#/definitions/address"}, "logo": {"$ref": "common#/definitions/logo"}, "createdBy": {"$ref": "common#/definitions/createdBy"}, "createdOn": {"$ref": "common#/definitions/createdOn"}, "lastChangedBy": {"$ref": "common#/definitions/lastChangedBy"}, "lastChangedOn": {"$ref": "common#/definitions/lastChangedOn"}}}