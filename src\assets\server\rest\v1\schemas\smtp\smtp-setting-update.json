{"$id": "smtp-setting-update", "title": "Update SMTP Settings", "type": "object", "properties": {"id": {"$ref": "common#/definitions/id"}, "identifier": {"type": "string", "sanitize": "mongo"}, "type": {"type": "string", "sanitize": "mongo"}, "smtp": {"type": "object", "properties": {"host": {"type": "string", "sanitize": "mongo"}, "port": {"type": "integer", "minimum": 1, "maximum": 65535}, "secure": {"type": "boolean"}, "requireTLS": {"type": "boolean"}, "user": {"type": "string", "sanitize": "mongo"}, "password": {"type": "string", "sanitize": "mongo"}, "from": {"type": "string", "sanitize": "mongo"}}, "required": ["host", "port", "secure", "user", "password", "from", "requireTLS"]}}, "required": ["id", "identifier", "type"]}