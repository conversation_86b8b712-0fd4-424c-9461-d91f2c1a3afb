{"$id": "stop-transaction-request-15", "description": "StopTransactionRequest 1.5", "type": "object", "properties": {"transactionId": {"type": "integer", "description": "number defining the transaction identifier"}, "idTag": {"type": "string", "description": "string defining identification token, e.g. RFID or credit card number. To be treated as case insensitive.", "minLength": 0, "maxLength": 20, "transform": ["toUpperCase"]}, "timestamp": {"type": "string", "format": "date-time", "customType": "date", "description": "string defining the status notification timestamp"}, "meterStop": {"type": "number", "description": "number defining the meter stop of the transaction"}, "transactionData": {"type": "object", "properties": {"values": {"type": "array", "items": {"timestamp": {"type": "string", "format": "date-time", "customType": "date", "description": "defining the timestamp of the meter value"}, "value": {"type": "object", "properties": {"$value": {"type": "string"}, "attributes": {"type": "object", "properties": {"context": {"type": "string", "description": "string defining the context of the SampledValue", "enum": ["Interruption.Begin", "Interruption.End", "Sample.Clock", "Sample.Periodic", "Transaction.Begin", "Transaction.End"]}, "format": {"type": "string", "description": "string defining the format of the SampledValue", "enum": ["Raw", "SignedData"]}, "measurand": {"type": "string", "description": "string defining the measurand of the SampledValue", "enum": ["Energy.Active.Export.Register", "Energy.Active.Import.Register", "Energy.Reactive.Export.Register", "Energy.Reactive.Import.Register", "Energy.Active.Export.Interval", "Energy.Active.Import.Interval", "Energy.Reactive.Export.Interval", "Energy.Reactive.Import.Interval", "Power.Active.Export", "Power.Active.Import", "Power.Reactive.Export", "Power.Reactive.Import", "Current.Export", "Current.Import", "Voltage", "Temperature"]}, "location": {"type": "string", "description": "string defining the location of the SampledValue", "enum": ["Inlet", "Outlet", "Body"]}, "unit": {"type": "string", "description": "string defining the unit of the SampledValue", "enum": ["Wh", "kWh", "varh", "k<PERSON>h", "W", "kW", "var", "kvar", "Amp", "Volt", "<PERSON><PERSON><PERSON>"]}}}}}}}}}, "performanceID": {"type": "string", "description": "performance correlation ID"}}, "required": ["transactionId", "timestamp", "meterStop"]}