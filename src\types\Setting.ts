import CreatedUpdatedProps from './CreatedUpdatedProps';
import { SettingAuthorizationActions } from './Authorization';
import Tax from './Tax';
import { TenantComponents } from './Tenant';

export enum TechnicalSettings {
  CRYPTO = 'crypto',
  USER = 'user',
  TASK = 'task',
}

export enum IntegrationSettings {
  OCPI = 'ocpi',
  OICP = 'oicp',
  ANALYTICS = 'analytics',
  SMART_CHARGING = 'smartCharging',
  REFUND = 'refund',
  PRICING = 'pricing',
  ASSET = 'asset',
  CAR_CONNECTOR = 'carConnector',
  BILLING = 'billing',
  BILLING_PLATFORM = 'billingPlatform',
  CAR = 'car',
  ORGANIZATION = 'organization',
  STATISTICS = 'statistics'
}

export interface Setting extends SettingAuthorizationActions, CreatedUpdatedProps {
  id?: string;
  identifier: TenantComponents | TechnicalSettings;
  sensitiveData?: string[];
  backupSensitiveData?: Record<string, unknown>;
  category?: 'business' | 'technical';
}

export interface SettingDB extends CreatedUpdatedProps, Setting {
  content?: SettingDBContent;
}

export interface SettingLink {
  id: string;
  name: string;
  description: string;
  role: string;
  url: string;
}

export interface SettingDBContent {
  type:
  RoamingSettingsType
  | AnalyticsSettingsType
  | RefundSettingsType
  | PricingSettingsType
  | BillingSettingsType
  | SmartChargingSettingsType
  | AssetSettingsType
  | SmartChargingContentType
  | CryptoSettingsType
  | UserSettingsType
  | CarConnectorSettingsType
  | TaskSettingsType
  | SmsVendor
  | MFASettingsType
  | SmtpVendor ;

  ocpi?: OcpiSetting;
  oicp?: OicpSetting;
  // pricing?: PricingSetting;  // TODO - reorg pricing similar to billing
  simple?: SimplePricingSetting;
  billing?: BillingSetting;
  stripe?: StripeBillingSetting;
  razorpay?: RazorpayBillingSetting;
  razorpayWallet?: RazorpayWalletBillingSetting;
  stripeWallet?: StripeWalletBillingSetting;
  tap?: TapBillingSetting;
  sac?: SacAnalyticsSetting;
  links?: SettingLink[];
  concur?: ConcurRefundSetting;
  sapSmartCharging?: SapSmartChargingSetting;
  asset?: AssetSetting;
  carConnector?: CarConnectorSetting;
  crypto?: CryptoSetting;
  user?: UserSetting;
  task?: TaskSetting;
  smsAla?: SmsAlaSmsSetting;
  textLocal?: TextLocalSmsSetting;
  smsGatewayHub?:SmsGatewayHubSmsSetting;
  mfa?: MfaSettings;
  smtp?: SmtpSetting;
}

export enum PricingSettingsType {
  SIMPLE = 'simple',
}

export interface PricingSettings extends Setting {
  identifier: TenantComponents.PRICING;
  type: PricingSettingsType;
  simple?: SimplePricingSetting;
}

// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface PricingSetting {
}

export enum PricingContentType {
  SIMPLE = 'simple',
}

export interface SimplePricingSetting extends PricingSetting {
  price: number;
  currency: string;
  last_updated?: Date;
}

export enum RoamingSettingsType {
  OCPI = 'ocpi',
  OICP = 'oicp',
}

export interface RoamingSettings extends Setting {
  identifier: TenantComponents.OCPI | TenantComponents.OICP;
  type: RoamingSettingsType;
  ocpi?: OcpiSetting;
  oicp?: OicpSetting;
}

export interface OcpiSetting {
  cpo: OcpiIdentifier;
  emsp: OcpiIdentifier;
  currency: string;
  businessDetails: OCPIBusinessDetails;
  tariffID?: string;
}

export interface OicpSetting {
  cpo: OicpIdentifier;
  emsp: OicpIdentifier;
  currency: string;
  businessDetails: OicpBusinessDetails;
}

export interface RoamingIdentifier {
  countryCode: string;
  partyID: string;
}

export type OcpiIdentifier = RoamingIdentifier;

// Should be renamed. Certificate and Key are bundled with OperatorID / ProviderID at this moment.
// Because the roles CPO and EMSP probably need different certificates to call the Hubject Backend
export interface OicpIdentifier extends RoamingIdentifier {
  key?: string;
  cert?: string;
}

export interface OCPIBusinessDetails {
  name: string;
  website: string;
  logo?: {
    url: string;
    thumbnail: string;
    category: string;
    type: string;
    width: number;
    height: number;
  };
}

export interface OicpBusinessDetails {
  name: string;
  website: string;
  logo?: {
    url: string;
    thumbnail: string;
    category: string;
    type: string;
    width: string;
    height: string;
  };
}

export enum AnalyticsSettingsType {
  SAC = 'sac'
}

export interface AnalyticsSettings extends Setting {
  identifier: TenantComponents.ANALYTICS;
  type: AnalyticsSettingsType;
  sac?: SacAnalyticsSetting;
  links: SettingLink[];
}

export interface SacAnalyticsSetting {
  mainUrl: string;
  timezone: string;
}

export enum SmartChargingSettingsType {
  SAP_SMART_CHARGING = 'sapSmartCharging'
}

export interface SmartChargingSettings extends Setting {
  identifier: TenantComponents.SMART_CHARGING;
  type: SmartChargingSettingsType;
  sapSmartCharging?: SapSmartChargingSetting;
}

// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface SmartChargingSetting {
}

export enum SmartChargingContentType {
  SAP_SMART_CHARGING = 'sapSmartCharging',
}

export interface SapSmartChargingSetting extends SmartChargingSetting {
  optimizerUrl: string;
  user: string;
  password: string;
  stickyLimitation: boolean;
  limitBufferDC: number;
  limitBufferAC: number;
  prioritizationParametersActive?: boolean;
}

export enum RefundSettingsType {
  CONCUR = 'concur',
}

export interface RefundSettings extends Setting {
  identifier: TenantComponents.REFUND;
  type: RefundSettingsType;
  concur?: ConcurRefundSetting;
}

// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface RefundSetting {
}

export interface ConcurRefundSetting extends RefundSetting {
  authenticationUrl: string;
  apiUrl: string;
  appUrl: string;
  clientId: string;
  clientSecret: string;
  paymentTypeId: string;
  expenseTypeCode: string;
  policyId: string;
  reportName: string;
}

export enum BillingSettingsType {
  STRIPE = 'stripe',
  RAZORPAY = 'razorpay',
  STRIPE_WALLET = 'stripe-wallet',
  TAP = 'tap',
  RAZORPAY_WALLET = 'razorpay-wallet',
}

export interface BillingSettings extends Setting {
  identifier: TenantComponents.BILLING;
  type: BillingSettingsType;
  billing: BillingSetting;
  stripe?: StripeBillingSetting;
  razorpay?: RazorpayBillingSetting;
  stripeWallet?: StripeWalletBillingSetting;
  tap?: TapBillingSetting;
  razorpayWallet?: RazorpayWalletBillingSetting;
}

export interface BillingSetting {
  isTransactionBillingActivated: boolean;
  immediateBillingAllowed: boolean;
  periodicBillingAllowed: boolean;
  taxID: string;
  platformFeeTaxID?: string;
  usersLastSynchronizedOn?: Date;
  taxes?: string[];
}

export interface StripeBillingSetting {
  url: string;
  secretKey: string;
  publicKey: string;
}

export interface TapBillingSetting {
  url: string;
  secretKey: string;
  publicKey: string;
  publicKeyWeb?: string;
}

export interface RazorpayBillingSetting {
  url: string;
  secretKey: string;
  publicKey: string;
}

export interface RazorpayWalletBillingSetting {
  url?: string;
  secretKey?: string;
  publicKey?: string;
  apiURL?: string;
  program?: string;
}

export interface StripeWalletBillingSetting {
  url: string;
  secretKey: string;
  publicKey: string;
}

export enum BillingContentType {
  STRIPE = 'stripe',
}

export interface AssetSettings extends Setting {
  identifier: TenantComponents.ASSET;
  type: AssetSettingsType;
  asset?: AssetSetting;
}

export enum AssetSettingsType {
  ASSET = 'asset',
}

export interface AssetSetting {
  connections: AssetConnectionSetting[];
}

export interface AssetConnectionSetting {
  id: string;
  name: string;
  description: string;
  url: string;
  timestamp: Date;
  type: AssetConnectionType;
  refreshIntervalMins?: number;
  schneiderConnection?: AssetSchneiderConnectionType;
  greencomConnection?: AssetGreencomConnectionType;
  iothinkConnection?: AssetIothinkConnectionType;
  witConnection?: AssetWitConnectionType;
  lacroixConnection?: AssetLacroixConnectionType
}

export enum AssetConnectionType {
  SCHNEIDER = 'schneider',
  GREENCOM = 'greencom',
  IOTHINK = 'iothink',
  WIT = 'wit',
  LACROIX = 'lacroix'
}

export interface AssetUserPasswordConnectionType {
  user: string;
  password: string;
}

export interface AssetGreencomConnectionType {
  clientId: string;
  clientSecret: string;
}

export type AssetSchneiderConnectionType = AssetUserPasswordConnectionType;

export type AssetIothinkConnectionType = AssetUserPasswordConnectionType;

export type AssetLacroixConnectionType = AssetUserPasswordConnectionType;

export interface AssetWitConnectionType extends AssetUserPasswordConnectionType {
  clientId: string;
  clientSecret: string;
  authenticationUrl: string;
}

export enum CarConnectorSettingsType {
  CAR_CONNECTOR = 'carConnector',
}

export interface CarConnectorSettings extends Setting {
  identifier: TenantComponents.CAR_CONNECTOR;
  type: CarConnectorSettingsType;
  carConnector?: CarConnectorSetting;
}

export interface CarConnectorSetting {
  connections: CarConnectorConnectionSetting[];
}

export interface CarConnectorConnectionSetting {
  id: string;
  name: string;
  description: string;
  timestamp: Date;
  type: CarConnectorConnectionType;
  token?: CarConnectorConnectionToken;
  mercedesConnection?: CarConnectorMercedesConnectionType;
  tronityConnection?: CarConnectorTronityConnectionType;
  targaTelematicsConnection?: CarConnectorTargaTelematicsConnectionType;
}

export enum CarConnectorConnectionType {
  NONE = '',
  MERCEDES = 'mercedes',
  TRONITY = 'tronity',
  TARGA_TELEMATICS = 'targaTelematics'
}

export interface OAuth2ConnectionType {
  clientId: string;
  clientSecret: string;
}

export interface CarConnectorMercedesConnectionType extends OAuth2ConnectionType {
  authenticationUrl: string;
  apiUrl: string;
}

export interface CarConnectorTronityConnectionType extends OAuth2ConnectionType {
  apiUrl: string;
}

export interface CarConnectorTargaTelematicsConnectionType extends OAuth2ConnectionType {
  authenticationUrl: string;
  apiUrl: string;
}

export interface CarConnectorConnectionToken {
  accessToken: string,
  expires: Date,
  tokenType?: string,
  expiresIn?: number,
  userName?: string,
  issued?: Date,
}

export enum CryptoSettingsType {
  CRYPTO = 'crypto'
}

export interface CryptoSettings extends Setting {
  identifier: TechnicalSettings.CRYPTO;
  type: CryptoSettingsType;
  crypto: CryptoSetting;
}

export interface CryptoKeyProperties {
  blockCypher: string;
  blockSize: number;
  operationMode: string;
}

export interface CryptoSetting {
  key: string;
  keyProperties: CryptoKeyProperties;
  formerKey?: string;
  formerKeyProperties?: CryptoKeyProperties;
  migrationToBeDone?: boolean;
}

export enum UserSettingsType {
  USER = 'user',
}

export interface UserSettings extends Setting {
  identifier: TechnicalSettings.USER;
  type: UserSettingsType;
  user?: UserSetting;
}

export interface UserSetting {
  autoActivateAccountAfterValidation: boolean;
}

export enum TaskSettingsType {
  TASK = 'task',
}

export interface TaskSettings extends Setting {
  identifier: TechnicalSettings.TASK;
  type: TaskSettingsType;
  task?: TaskSetting;
}

export interface TaskSetting {
  disableAllTasks?: boolean;
  disableTasksInEnv?: string[];
}

export enum SmsVendor {
  TEXTLOCAL = 'textLocal',
  SMSALA = 'smsAla',
  SMSGATEWAYHUb = 'smsGatewayHub',
}
export enum SmtpVendor {
  SMTP='smtp'
}

export interface SmsAlaSmsSetting{
  apiId: string;
  apiPassword: string;
  senderId: string;
}


export interface SmtpSetting{
  from?: string;
  host?: string;
  port?: number;
  secure?: boolean;
  requireTLS?: boolean;
  user?: string;
  password?: string;
}


export interface TextLocalSmsSetting{
  apiId: string;
  apiPassword?: string;
  senderId: string;
}

export interface SmsGatewayHubSmsSetting {
  apiId: string;
  apiPassword?: string;
  senderId: string;
}

export enum  MFASettingsType{
  MFA = 'mfa',
}

export interface MfaSettings{
  MFAStatus :boolean;

}

export interface SmsSettings extends Setting {
  identifier: TenantComponents.SMS;
  type: SmsVendor;
  smsAla?: SmsAlaSmsSetting;
  textLocal?: TextLocalSmsSetting;
  smsGatewayHub?: SmsGatewayHubSmsSetting;
}


export interface SmtpSettings extends Setting {
  identifier: TenantComponents.SMTP;
  type: SmtpVendor;
  smtp?: SmtpSetting;
}
