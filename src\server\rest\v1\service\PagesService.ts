import { Action, Entity } from '../../../../types/Authorization';
import { NextFunction, Request, Response } from 'express';
import Constants from '../../../../utils/Constants';
import Logging from '../../../../utils/Logging';
import { ServerAction } from '../../../../types/Server';
import PagesValidatorRest from '../validator/PagesValidatorRest';
import UtilsService from './UtilsService';
import Pages from '../../../../types/Pages';
import PagesStorage from '../../../../storage/mongodb/PagesStorage';
import AuthorizationService from './AuthorizationService';

const MODULE_NAME = 'PagesService';


export default class PagesService {
  public static async handleCreatepages(action: ServerAction, req: Request, res: Response, next: NextFunction): Promise<void> {
    //Filter
    const filteredRequest = PagesValidatorRest.getInstance().validateCreatePagesReq(req.body);
    //Check for dynamic authorizations (, depending on your logic)
    const authorizations = await AuthorizationService.checkAndGetPagesAuthorizations(
      req.tenant, req.user, {}, Action.CREATE, filteredRequest
    );
    // Prepare the new page
    const newPage = {
      ...filteredRequest,
      title: filteredRequest.title,
      slug: filteredRequest.slug,
      description: filteredRequest.description,
      status: filteredRequest.status,
      createdBy: { id: req.user.id },
      createdOn: new Date(),
    };
    // Save the page to the database
    const newPageId = await PagesStorage.savePages(req.tenant, newPage, true);
    newPage.id = newPageId;
    // Log the creation
    await Logging.logInfo({
      tenantID: req.tenant.id,
      user: req.user,
      actionOnUser: req.user,
      module: MODULE_NAME,
      method: 'handleCreatePages',
      message: `Page with ID '${newPageId}' has been created successfully`,
      action: action
    });

    // Respond with success
    res.json(Object.assign({ id: newPageId }, Constants.REST_RESPONSE_SUCCESS));
    next();
  }
  public static async handleGetpages(action: ServerAction, req: Request, res: Response, next: NextFunction): Promise<void> {
    //filter
    const filteredRequest = PagesValidatorRest.getInstance().validateGetPagesReq(req.query);
    //dynamic auth
    const authorizationPagesFilters = await AuthorizationService.checkAndGetPagesAuthorization(req.tenant, req.user, Action.LIST, filteredRequest);
    // Get pages
    const Pages = await PagesStorage.getPages(req.tenant,
      {
        search: filteredRequest.Search,
        ...authorizationPagesFilters.filters
      },
      {
        limit: filteredRequest.Limit,
        skip: filteredRequest.Skip,
        sort: UtilsService.httpSortFieldsToMongoDB(filteredRequest.SortFields),
        onlyRecordCount: filteredRequest.OnlyRecordCount
      },
      authorizationPagesFilters.projectFields
    );
    res.send(Pages)
    next()
  }


  public static async handleUpdatePages(action: ServerAction, req: Request, res: Response, next: NextFunction): Promise<void> {
    // Filter the request
    const filteredRequest = PagesValidatorRest.getInstance().validateUserUpdateReq(req.body);
       // Check and Get Page
    let page = await UtilsService.checkAndGetPagesAuthorization(
      req.tenant, req.user, filteredRequest.id, Action.UPDATE, action, filteredRequest);
    //   // Update page fields
    page.title = filteredRequest.title;
    page.description = filteredRequest.description;
    page.status = filteredRequest.status;

  // Remove slug to prevent it from being updated
    delete filteredRequest.slug;

      // Update the last changed By
    const lastChangedBy = { id: req.user.id };
    const lastChangedOn = new Date();

    page= {
      ...page,
      ...filteredRequest,
      lastChangedBy: lastChangedBy,
      lastChangedOn: lastChangedOn,
    };
      // Update the Page
    await PagesStorage.savePages(req.tenant, page);
      // Log the update
    await Logging.logInfo({
      tenantID: req.tenant.id,
      user: req.user, module: MODULE_NAME, method: 'handleUpdatePage',
      message: `Page '${page.title}' has been updated successfully`,
      action: action,
      detailedMessages: { page }
    });
    // Respond with success
    res.json(Constants.REST_RESPONSE_SUCCESS);
    next();
  }


  public static async handlesoftdeletePages(action: ServerAction, req: Request, res: Response, next: NextFunction): Promise<void> {
      // Filter the request
    const filteredRequest = PagesValidatorRest.getInstance().validatePagesDeleteReq(req.query);
      //  // Check and Get pages
       const page = await UtilsService.checkAndGetPagesAuthorization(req.tenant, req.user, filteredRequest.ID, Action.DELETE, action, null, {}, false);
         // Set isDeleted field to true
         page.isDeleted = true;
         await PagesStorage.savePages(req.tenant, page, true);
          // 
        res.json(Constants.REST_RESPONSE_SUCCESS);
        next();
      }

    }





