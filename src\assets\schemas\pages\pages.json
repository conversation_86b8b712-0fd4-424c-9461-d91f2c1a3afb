{"$id": "pages", "definitions": {"id": {"$ref": "common#/definitions/id"}, "title": {"type": "string", "sanitize": "mongo", "minLength": 1, "maxLength": 1000, "description": "Title of the terms and conditions."}, "slug": {"type": "string", "sanitize": "mongo", "pattern": "^[a-z0-9]+(?:-[a-z0-9]+)*$", "description": "URL-friendly identifier for the terms and conditions."}, "description": {"type": "string", "sanitize": "mongo", "minLength": 1, "maxLength": 300000, "description": "Detailed description  of the terms and conditions."}, "status": {"type": "string", "enum": ["active", "inactive"], "default": "inactive", "description": "Status of the terms and conditions."}, "createdDate": {"type": "string", "format": "date-time", "description": "Timestamp of when the terms and conditions were created."}, "updatedDate": {"type": "string", "format": "date-time", "description": "Timestamp of when the terms and conditions were last updated."}}}