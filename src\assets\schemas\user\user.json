{"$id": "user", "definitions": {"id": {"$ref": "common#/definitions/id"}, "name": {"type": "string", "minLength": 1, "maxLength": 100, "transform": ["toUpperCase"], "sanitize": "mongo"}, "firstName": {"type": "string", "minLength": 1, "maxLength": 100, "sanitize": "mongo"}, "email": {"$ref": "common#/definitions/email"}, "password": {"type": "string", "sanitize": "mongo", "pattern": "(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!#@:;,%&=_<>\\/'\\$\\^\\*\\.\\?\\-\\+\\(\\)])(?=.{8,})"}, "costCenter": {"type": "string", "sanitize": "mongo"}, "iNumber": {"type": "string", "sanitize": "mongo"}, "image": {"type": "string", "sanitize": "mongo"}, "countryCode": {"type": "string", "sanitize": "mongo"}, "mobile": {"type": "string", "sanitize": "mongo", "pattern": "^|\\+?([0-9] ?){4,14}[0-9]$", "nullable": true}, "locale": {"type": "string", "sanitize": "mongo"}, "address": {"$ref": "common#/definitions/address"}, "phone": {"type": "string", "sanitize": "mongo", "pattern": "^|\\+?([0-9] ?){9,14}[0-9]$", "nullable": true}, "issuer": {"type": "boolean", "sanitize": "mongo"}, "notificationsActive": {"type": "boolean", "sanitize": "mongo"}, "status": {"type": "string", "sanitize": "mongo"}, "plateID": {"type": "string", "sanitize": "mongo", "pattern": "^[A-Z0-9- ]*$"}, "role": {"type": "string", "sanitize": "mongo", "enum": ["S", "A", "B", "D", "C", "E", "CA", "TA", "TM"]}, "notifications": {"type": "object", "properties": {"sendSessionStarted": {"type": "boolean", "sanitize": "mongo"}, "sendOptimalChargeReached": {"type": "boolean", "sanitize": "mongo"}, "sendEndOfCharge": {"type": "boolean", "sanitize": "mongo"}, "sendEndOfSession": {"type": "boolean", "sanitize": "mongo"}, "sendUserAccountStatusChanged": {"type": "boolean", "sanitize": "mongo"}, "sendSessionNotStarted": {"type": "boolean", "sanitize": "mongo"}, "sendCarCatalogSynchronizationFailed": {"type": "boolean", "sanitize": "mongo"}, "sendUserAccountInactivity": {"type": "boolean", "sanitize": "mongo"}, "sendPreparingSessionNotStarted": {"type": "boolean", "sanitize": "mongo"}, "sendBillingSynchronizationFailed": {"type": "boolean", "sanitize": "mongo"}, "sendBillingPeriodicOperationFailed": {"type": "boolean", "sanitize": "mongo"}, "sendBillingNewInvoice": {"type": "boolean", "sanitize": "mongo"}, "sendNewRegisteredUser": {"type": "boolean", "sanitize": "mongo"}, "sendUnknownUserBadged": {"type": "boolean", "sanitize": "mongo"}, "sendChargingStationStatusError": {"type": "boolean", "sanitize": "mongo"}, "sendChargingStationRegistered": {"type": "boolean", "sanitize": "mongo"}, "sendOcpiPatchStatusError": {"type": "boolean", "sanitize": "mongo"}, "sendOicpPatchStatusError": {"type": "boolean", "sanitize": "mongo"}, "sendOfflineChargingStations": {"type": "boolean", "sanitize": "mongo"}, "sendEndUserErrorNotification": {"type": "boolean", "sanitize": "mongo"}, "sendComputeAndApplyChargingProfilesFailed": {"type": "boolean", "sanitize": "mongo"}, "sendAccountVerificationNotification": {"type": "boolean", "sanitize": "mongo"}, "sendAdminAccountVerificationNotification": {"type": "boolean", "sanitize": "mongo"}}}, "mobileToken": {"type": "string", "sanitize": "mongo"}, "mobileOS": {"type": "string", "sanitize": "mongo", "enum": ["ios", "android", "windows", "macos", "web"]}, "mobileBundleID": {"type": "string", "sanitize": "mongo"}, "mobileAppName": {"type": "string", "sanitize": "mongo"}, "mobileVersion": {"type": "string", "sanitize": "mongo"}, "deleted": {"type": "boolean", "sanitize": "mongo"}, "autocharge": {"type": "boolean", "sanitize": "mongo"}, "passwordWrongNbrTrials": {"type": "number", "sanitize": "mongo"}, "MFAAdded": {"type": "boolean", "default": false, "sanitize": "mongo"}, "mfaqrcode": {"type": "string", "sanitize": "mongo"}, "mfacode": {"type": "string", "pattern": "^[0-9]{6}$"}, "corporateID": {"type": "string", "sanitize": "mongo"}, "companyID": {"$ref": "company#/definitions/id"}}}