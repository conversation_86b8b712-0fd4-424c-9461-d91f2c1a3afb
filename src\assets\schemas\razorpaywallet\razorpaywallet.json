{"$id": "razorpaywallet", "definitions": {"id": {"type": "string", "sanitize": "mongo"}, "name": {"type": "string", "minLength": 1, "maxLength": 100, "transform": ["toUpperCase"], "sanitize": "mongo"}, "email": {"$ref": "common#/definitions/email"}, "contact": {"type": "string", "sanitize": "mongo", "pattern": "^|\\+?([0-9] ?){4,14}[0-9]$", "nullable": true}, "identification_id": {"type": "string", "minLength": 1, "maxLength": 100, "sanitize": "mongo"}, "identification_type": {"type": "string", "sanitize": "mongo", "enum": ["pan", "voter_id", "passport", "driving_licence"]}, "date_of_birth": {"type": "string", "minLength": 1, "maxLength": 100, "sanitize": "mongo"}, "otp": {"type": "string", "minLength": 1, "maxLength": 100, "sanitize": "mongo"}, "application_id": {"type": "string", "minLength": 1, "maxLength": 100, "sanitize": "mongo"}, "account_id": {"type": "string", "minLength": 1, "maxLength": 100, "sanitize": "mongo"}, "activation_token": {"type": "string", "minLength": 1, "maxLength": 100, "sanitize": "mongo"}, "amount": {"type": "string", "minLength": 1, "maxLength": 100, "sanitize": "mongo"}, "reference_id": {"type": "string", "minLength": 1, "maxLength": 100, "sanitize": "mongo"}, "category": {"type": "string", "minLength": 1, "maxLength": 100, "sanitize": "mongo"}, "user_load": {"type": "boolean", "sanitize": "mongo"}, "preauth_token_id": {"type": "string", "minLength": 1, "maxLength": 100, "sanitize": "mongo"}, "bank_account_number": {"type": "string", "minLength": 1, "maxLength": 100, "sanitize": "mongo"}, "ifsc": {"type": "string", "minLength": 1, "maxLength": 100, "sanitize": "mongo"}, "beneficiary_id": {"type": "string", "minLength": 1, "maxLength": 100, "sanitize": "mongo"}}}