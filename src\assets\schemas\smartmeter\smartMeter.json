{"$id": "smartmeter", "definitions": {"id": {"$ref": "common#/definitions/id"}, "name": {"type": "string", "sanitize": "mongo"}, "siteAreaID": {"$ref": "common#/definitions/id"}, "assetType": {"type": "string", "sanitize": "mongo", "minLength": 1, "maxLength": 255, "description": "Name of the smart meter vendor."}, "fluctuationPercent": {"type": "number", "sanitize": "mongo", "minimum": 0, "maximum": 100}, "staticValueWatt": {"type": "number", "sanitize": "mongo"}, "image": {"type": "string", "sanitize": "mongo"}, "dynamicAsset": {"type": "boolean", "sanitize": "mongo"}, "usesPushAPI": {"type": "boolean", "sanitize": "mongo"}, "coordinates": {"type": "array", "items": {"type": "number", "sanitize": "mongo"}}, "connectionID": {"type": "string", "sanitize": "mongo"}, "meterID": {"type": "string", "sanitize": "mongo"}, "excludeFromSmartCharging": {"type": "boolean", "sanitize": "mongo"}, "variationThresholdPercent": {"type": "number", "sanitize": "mongo", "minimum": 0, "maximum": 100}, "meterName": {"type": "string", "sanitize": "mongo"}, "publicKey": {"type": "string", "sanitize": "mongo"}, "privateKey": {"type": "string", "sanitize": "mongo"}, "url": {"type": "string", "format": "uri", "sanitize": "mongo"}}}