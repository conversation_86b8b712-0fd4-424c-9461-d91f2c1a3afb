import Schema from '../../../../types/validator/Schema';
import SchemaValidator from '../../../../validator/SchemaValidator';
import fs from 'fs';
import global from '../../../../types/GlobalType';
import { HttpPagesCreateRequest, HttpPagesDeleteRequest, HttpPagesGetRequest, HttpPagesUpdateRequest } from '../../../../types/requests/HttpPagesRequest ';

export default class PagesValidatorRest extends SchemaValidator {
  private static instance: PagesValidatorRest | null = null;
  private createpages: Schema = JSON.parse(fs.readFileSync(`${global.appRoot}/assets/server/rest/v1/schemas/pages/pages-create.json`, 'utf8'));
  private getpages: Schema = JSON.parse(fs.readFileSync(`${global.appRoot}/assets/server/rest/v1/schemas/pages/pages-get.json`, 'utf8'));
  private updatepages: Schema = JSON.parse(fs.readFileSync(`${global.appRoot}/assets/server/rest/v1/schemas/pages/pages-update.json`, 'utf8'));
  private pagessoftdelete: Schema = JSON.parse(fs.readFileSync(`${global.appRoot}/assets/server/rest/v1/schemas/pages/pages-soft-delete.json`, 'utf8'));
  private constructor() {
    super('PagesValidatorRest');
  }

  public static getInstance(): PagesValidatorRest {
    if (!PagesValidatorRest.instance) {
        PagesValidatorRest.instance = new PagesValidatorRest();
    }
    return PagesValidatorRest.instance;
  }

  public validateCreatePagesReq(data: Record<string, unknown>): HttpPagesCreateRequest {
    return this.validate(this.createpages, data);
  }
  public validateGetPagesReq(data: Record<string, unknown>): HttpPagesGetRequest{
    return this.validate(this.getpages, data);
  }
  public validateUserUpdateReq(data: Record<string, unknown>): HttpPagesUpdateRequest{
      return this.validate(this.updatepages, data);

    }
    public validatePagesDeleteReq(data: Record<string, unknown>): HttpPagesDeleteRequest{
        return this.validate(this.pagessoftdelete, data);
  
      }
  }

