{"$id": "smart-meter-get", "title": "Get Smart Meters Request", "type": "object", "properties": {"WithSite": {"type": "boolean", "sanitize": "mongo"}, "WithSiteArea": {"type": "boolean", "sanitize": "mongo"}, "WithNoSiteArea": {"type": "boolean", "sanitize": "mongo"}, "SiteAreaID": {"$ref": "common#/definitions/ids"}, "SiteID": {"$ref": "common#/definitions/ids"}, "DynamicOnly": {"type": "boolean", "sanitize": "mongo"}, "Issuer": {"type": "boolean", "sanitize": "mongo"}, "Search": {"$ref": "common#/definitions/search"}, "Limit": {"$ref": "common#/definitions/limit"}, "SortFields": {"$ref": "common#/definitions/sortFields"}, "Skip": {"$ref": "common#/definitions/skip"}, "OnlyRecordCount": {"$ref": "common#/definitions/onlyRecordCount"}, "ProjectFields": {"$ref": "common#/definitions/projectFields"}, "WithAuth": {"$ref": "common#/definitions/withAuth", "default": true}, "MeterID": {"type": "string", "sanitize": "mongo"}, "MeterName": {"type": "string", "sanitize": "mongo"}, "ExcludeFromSmartCharging": {"type": "boolean", "sanitize": "mongo"}}, "required": ["Limit"]}