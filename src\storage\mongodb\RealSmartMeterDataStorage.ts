import global from '../../types/GlobalType';
import Tenant from '../../types/Tenant';

const MODULE_NAME = 'RealSmartMeterDataStorage';

export default class RealSmartMeterDataStorage {
  public static async saveData(tenant: Tenant, data: any): Promise<any> {
    const collection = global.database.getCollection<any>(tenant.id, 'realsmartmerterdata');

    if (Array.isArray(data)) {
    
      const dataWithTimestamps = data.map(item => ({
        ...item,
        createdAt: new Date()
      }));
// Insert multiple objects
      const result = await collection.insertMany(dataWithTimestamps);
      return {
        insertedCount: result.insertedCount,
        insertedIds: result.insertedIds
      };
    } else {
      // Single object insert
      const result = await collection.insertOne({
        ...data,
        createdAt: new Date()
      });
      return {
        insertedId: result.insertedId
      };
    }
  }
}
