import Schema from '../../../../types/validator/Schema';
import SchemaValidator from '../../../../validator/SchemaValidator';
import fs from 'fs';
import Asset from '../../../../types/Asset';
import global from '../../../../types/GlobalType';
import {
  HttpSmartMeterCreateRequest,
  HttpSmartMeterGetRequest,
  HttpSmartMeterUpdateRequest,
  HttpSmartMeterDeleteRequest
} from '../../../../types/requests/HttpSmartMeterRequest';

import { HttpAssetCheckConnection, HttpAssetConsumptionGetRequest, HttpAssetDeleteRequest, HttpAssetGetRequest, HttpAssetImageGetRequest, HttpAssetsGetRequest, HttpAssetsInErrorGetRequest } from '../../../../types/requests/HttpAssetRequest';

export default class SmartMeterValidatorRest extends SchemaValidator {
  private static instance: SmartMeterValidatorRest | null = null;

  private createSmartMeter: Schema = JSON.parse(fs.readFileSync(`${global.appRoot}/assets/server/rest/v1/schemas/smart-meter/smart-meter-create.json`, 'utf8'));
  private getSmartMeter: Schema = JSON.parse(fs.readFileSync(`${global.appRoot}/assets/server/rest/v1/schemas/smart-meter/smart-meter-get.json`, 'utf8'));
  private getSmartMeters: Schema = JSON.parse(fs.readFileSync(`${global.appRoot}/assets/server/rest/v1/schemas/smart-meter/smart-meters-get.json`, 'utf8'));
 private updateSmartMeter: Schema = JSON.parse(fs.readFileSync(`${global.appRoot}/assets/server/rest/v1/schemas/smart-meter/smart-meter-update.json`, 'utf8'));
 private deleteSmartMeter: Schema = JSON.parse(fs.readFileSync(`${global.appRoot}/assets/server/rest/v1/schemas/smart-meter/smart-meter-soft-delete.json`, 'utf8'));

  private constructor() {
    super('SmartMeterValidatorRest');
  }

  public static getInstance(): SmartMeterValidatorRest {
    if (!SmartMeterValidatorRest.instance) {
      SmartMeterValidatorRest.instance = new SmartMeterValidatorRest();
    }
    return SmartMeterValidatorRest.instance;
  }

public validateCreateSmartMeterReq(data: Record<string, unknown>): Asset {
    return this.validate(this.createSmartMeter, data);
  }
//39
   public validateSmartMetersGetReq(data: Record<string, unknown>): HttpAssetsGetRequest {
     return this.validate(this.getSmartMeter, data);
   }
  public validateSmartMeterGetReq(data: Record<string, unknown>): HttpAssetGetRequest {
    return this.validate(this.getSmartMeters, data);
  }

  public validateSmartMeterUpdateReq(data: Record<string, unknown>): Asset {
    return this.validate(this.updateSmartMeter, data);
  }

public validateSmartMeterDeleteReq(data: Record<string, unknown>): HttpAssetDeleteRequest {
    return this.validate(this.deleteSmartMeter, data);
  }
}
