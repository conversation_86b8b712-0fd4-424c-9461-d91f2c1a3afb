{"$id": "start-transaction-request", "description": "StartTransactionRequest", "type": "object", "properties": {"connectorId": {"type": "integer", "description": "number defining the connector identifier of the charge point"}, "idTag": {"type": "string", "description": "string defining identification token, e.g. RFID or credit card number. To be treated as case insensitive.", "minLength": 1, "maxLength": 20, "transform": ["toUpperCase"]}, "timestamp": {"type": "string", "format": "date-time", "customType": "date", "description": "string defining the status notification timestamp"}, "meterStart": {"type": "number", "description": "number defining the meter start of the transaction"}, "reservationId": {"type": "integer", "description": "number defining the reservation identifier of the transaction"}, "performanceID": {"type": "string", "description": "performance correlation ID"}}, "required": ["connectorId", "idTag", "timestamp", "meterStart"]}