
import Pages from "../Pages";
import HttpByIDRequest from "./HttpByIDRequest";
import HttpDatabaseRequest from "./HttpDatabaseRequest";

export interface HttpPagesCreateRequest extends Pages {
}

export interface HttpPagesUpdateRequest extends Pages{
}
export interface HttpPagesDeleteRequest extends HttpByIDRequest {
    ID: string;
  }
    



 export interface HttpPagesGetRequest extends HttpDatabaseRequest{
    id: string; 
    title: string;
    slug: string; 
    description?: string; 
    status?: string; 
    createdDate?: Date; 
    updatedDate?: Date;
    Search?:string;
}
