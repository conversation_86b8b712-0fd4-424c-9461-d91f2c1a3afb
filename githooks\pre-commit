#!/bin/sh

# Instructions:
# Put this file into your .git/hooks folder and set as executable
#   - for Windows (attrib +x pre-commit)
#   - for *nix (chmod +x pre-commit)

# echo "Starting the server"
# npm run start:dev

echo "Running linter fix:"
npm run eslint:fix

echo "Running unit tests (do not forget to start the server before with 'npm run start:dev'):"
# npm run start:email &
npm run mochatest

# Do not abort the commit because of errors
exit 0
