import { AuthorizationActions} from './Authorization';

import Address from './Address';
import { BillingUserData } from './Billing';
import CreatedUpdatedProps from './CreatedUpdatedProps';
import { ImportStatus } from './GlobalType';
import UserNotifications from './UserNotifications';
import { UserRole } from './User';

export default interface Pages extends CreatedUpdatedProps, AuthorizationActions  {
    id?: string; 
    title: string;
    slug: string; 
    description?: string; 
    status?: string; 
    createdDate?: Date; 
    updatedDate?: Date; 
    isDeleted?:boolean;
}
