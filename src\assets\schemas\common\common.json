{"$id": "common", "definitions": {"id": {"type": "string", "pattern": "^$|^[0-9a-fA-F]{24}$", "sanitize": "mongo"}, "otp": {"type": "string", "sanitize": "mongo"}, "ids": {"type": "string", "pattern": "^$|^([0-9a-fA-F]{24}\\|?)*$", "sanitize": "mongo"}, "string-id": {"type": "string", "pattern": "^[^|]*$", "sanitize": "mongo"}, "string-ids": {"type": "string", "pattern": "^(([^|]*)(\\||[^|])*)?$", "sanitize": "mongo"}, "number-id": {"type": "number", "sanitize": "mongo"}, "number-ids": {"type": "string", "pattern": "^(\\d\\|?)*$", "sanitize": "mongo"}, "tariff-id": {"type": "string", "maxLength": 36, "sanitize": "mongo", "nullable": true}, "owner-name": {"type": "string", "maxLength": 100, "sanitize": "mongo"}, "authSubdomain": {"type": "string", "pattern": "^[a-z0-9]*$", "maxLength": 20, "sanitize": "mongo"}, "subdomain": {"type": "string", "pattern": "^[a-z0-9]*$", "minLength": 1, "maxLength": 20, "sanitize": "mongo"}, "search": {"type": "string", "sanitize": "mongo"}, "limit": {"type": "integer", "sanitize": "mongo", "minimum": 1, "maximum": 1000}, "sortFields": {"type": "string", "sanitize": "mongo"}, "projectFields": {"type": "string", "sanitize": "mongo"}, "onlyRecordCount": {"type": "boolean", "sanitize": "mongo"}, "withAuth": {"type": "boolean", "sanitize": "mongo"}, "skip": {"type": "number", "sanitize": "mongo"}, "logo": {"type": "string", "sanitize": "mongo"}, "address": {"type": "object", "properties": {"address1": {"type": "string", "sanitize": "mongo"}, "address2": {"type": "string", "sanitize": "mongo"}, "postalCode": {"type": "string", "sanitize": "mongo"}, "city": {"type": "string", "sanitize": "mongo"}, "department": {"type": "string", "sanitize": "mongo"}, "region": {"type": "string", "sanitize": "mongo"}, "country": {"type": "string", "sanitize": "mongo"}, "coordinates": {"type": "array", "sanitize": "mongo"}}}, "acceptEula": {"type": "boolean", "sanitize": "mongo"}, "captcha": {"type": "string", "sanitize": "mongo"}, "language": {"type": "string", "sanitize": "mongo"}, "LocLongitude": {"type": "number", "format": "longitude", "sanitize": "mongo"}, "LocLatitude": {"type": "number", "format": "latitude", "sanitize": "mongo"}, "LocMaxDistanceMeters": {"type": "number", "sanitize": "mongo", "exclusiveMinimum": 0}, "email": {"type": "string", "format": "email", "sanitize": "mongo", "transform": ["toLowerCase"]}, "createdBy": {"type": "object", "nullable": true, "properties": {"id": {"$ref": "#/definitions/id"}}}, "createdOn": {"type": "object", "nullable": true, "sanitize": "mongo"}, "lastChangedBy": {"type": "object", "nullable": true, "properties": {"id": {"$ref": "#/definitions/id"}}}, "lastChangedOn": {"type": "object", "nullable": true, "sanitize": "mongo"}}}