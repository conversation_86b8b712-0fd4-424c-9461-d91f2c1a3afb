
import { ObjectId, UpdateResult } from 'mongodb';
import global, { DatabaseCount, FilterParams, Image } from '../../types/GlobalType';
import Asset from '../../types/Asset';
import { AssetInErrorType } from '../../types/InError';
import Constants from '../../utils/Constants';
import { DataResult } from '../../types/DataResult';
import DatabaseUtils from './DatabaseUtils';
import DbParams from '../../types/database/DbParams';
import Logging from '../../utils/Logging';
import Tenant from '../../types/Tenant';
import Utils from '../../utils/Utils';

const MODULE_NAME = 'SmartMeterStorage';

export default class SmartMeterStorage {
  public static async getsmartMeterAsset(tenant: Tenant, id: string = Constants.UNKNOWN_OBJECT_ID,
    params: { withSiteArea?: boolean, siteIDs?: string[], issuer?: boolean } = {}, projectFields?: string[]): Promise<Asset> {
    const assetsMDB = await SmartMeterStorage.getsmartMeterAssets(tenant, {
      assetIDs: [id],
      withSiteArea: params.withSiteArea,
      siteIDs: params.siteIDs,
      issuer: params.issuer
    }, Constants.DB_PARAMS_SINGLE_RECORD, projectFields);
    const asset = assetsMDB.count === 1 ? assetsMDB.result[0] : null;
    return asset;
  }

  public static async getAssetImage(tenant: Tenant, id: string): Promise<{ id: string; image: string }> {
    const startTime = Logging.traceDatabaseRequestStart();
    DatabaseUtils.checkTenantObject(tenant);
    const assetImageMDB = await global.database.getCollection<any>(tenant.id, 'assetimages')
      .findOne({ _id: DatabaseUtils.convertToObjectID(id) }) as Image;
    await Logging.traceDatabaseRequestEnd(tenant, MODULE_NAME, 'getAssetImage', startTime, { id }, assetImageMDB);
    return {
      id: id,
      image: assetImageMDB ? assetImageMDB.image : null
    };
  }

  public static async saveSmartMeterAsset(tenant: Tenant, assetToSave: Asset, saveImage = true): Promise<string> {
    const startTime = Logging.traceDatabaseRequestStart();
    DatabaseUtils.checkTenantObject(tenant);
    const assetMDB: any = {
      _id: assetToSave.id ? DatabaseUtils.convertToObjectID(assetToSave.id) : new ObjectId(),
      name: assetToSave.name,
      companyID: DatabaseUtils.convertToObjectID(assetToSave.companyID),
      siteID: DatabaseUtils.convertToObjectID(assetToSave.siteID),
      siteAreaID: DatabaseUtils.convertToObjectID(assetToSave.siteAreaID),
      coordinates: Utils.hasValidGpsCoordinates(assetToSave.coordinates) ? assetToSave.coordinates.map(
        (coordinate) => Utils.convertToFloat(coordinate)) : [],
      assetType: assetToSave.assetType,
      excludeFromSmartCharging: Utils.convertToBoolean(assetToSave.excludeFromSmartCharging),
      variationThresholdPercent: Utils.convertToFloat(assetToSave.variationThresholdPercent),
      powerWattsLastSmartChargingRun: Utils.convertToFloat(assetToSave.powerWattsLastSmartChargingRun),
      fluctuationPercent: Utils.convertToFloat(assetToSave.fluctuationPercent),
      staticValueWatt: Utils.convertToFloat(assetToSave.staticValueWatt),
      dynamicAsset: Utils.convertToBoolean(assetToSave.dynamicAsset),
      usesPushAPI: Utils.convertToBoolean(assetToSave.usesPushAPI),
      issuer: Utils.convertToBoolean(assetToSave.issuer),
      connectionID: assetToSave.connectionID,
      meterID: assetToSave.meterID,
      currentConsumptionWh: Utils.convertToFloat(assetToSave.currentConsumptionWh),
      currentInstantAmps: Utils.convertToFloat(assetToSave.currentInstantAmps),
      currentInstantAmpsL1: Utils.convertToFloat(assetToSave.currentInstantAmpsL1),
      currentInstantAmpsL2: Utils.convertToFloat(assetToSave.currentInstantAmpsL2),
      currentInstantAmpsL3: Utils.convertToFloat(assetToSave.currentInstantAmpsL3),
      currentInstantVolts: Utils.convertToFloat(assetToSave.currentInstantVolts),
      currentInstantVoltsL1: Utils.convertToFloat(assetToSave.currentInstantVoltsL1),
      currentInstantVoltsL2: Utils.convertToFloat(assetToSave.currentInstantVoltsL2),
      currentInstantVoltsL3: Utils.convertToFloat(assetToSave.currentInstantVoltsL3),
      currentInstantWatts: Utils.convertToFloat(assetToSave.currentInstantWatts),
      currentInstantWattsL1: Utils.convertToFloat(assetToSave.currentInstantWattsL1),
      currentInstantWattsL2: Utils.convertToFloat(assetToSave.currentInstantWattsL2),
      currentInstantWattsL3: Utils.convertToFloat(assetToSave.currentInstantWattsL3),
      currentStateOfCharge: Utils.convertToFloat(assetToSave.currentStateOfCharge),
      meterName: assetToSave.meterName,
      publicKey: assetToSave.publicKey,
      privateKey: assetToSave.privateKey,
      url: assetToSave.url,
    };
    if (assetToSave.lastConsumption) {
      assetMDB.lastConsumption = {
        value: Utils.convertToFloat(assetToSave.lastConsumption.value),
        timestamp: Utils.convertToDate(assetToSave.lastConsumption.timestamp)
      };
    }
    DatabaseUtils.addLastChangedCreatedProps(assetMDB, assetToSave);
    await global.database.getCollection<any>(tenant.id, 'smartmeters').findOneAndUpdate(
      { _id: assetMDB._id },
      { $set: assetMDB },
      { upsert: true }
    );
    if (saveImage) {
      await SmartMeterStorage.saveAssetImage(tenant, assetMDB._id.toString(), assetToSave.image);
    }
    await Logging.traceDatabaseRequestEnd(tenant, MODULE_NAME, 'saveSmartMeterAsset', startTime, assetMDB);
    return assetMDB._id.toString();
  }

  public static async getsmartMeterAssets(tenant: Tenant,
    params: any = {}, dbParams?: DbParams, projectFields?: string[]): Promise<DataResult<Asset>> {
    const startTime = Logging.traceDatabaseRequestStart();
    DatabaseUtils.checkTenantObject(tenant);
    dbParams = Utils.cloneObject(dbParams);
    dbParams.limit = Utils.checkRecordLimit(dbParams.limit);
    dbParams.skip = Utils.checkRecordSkip(dbParams.skip);
    const aggregation = [];
    const filters: FilterParams = {};

    if (params.search) {
      filters.$or = [
        { 'name': { $regex: params.search, $options: 'i' } },
      ];
      if (DatabaseUtils.isObjectID(params.search)) {
        filters.$or.push({ '_id': DatabaseUtils.convertToObjectID(params.search) });
      }
    }
    if (params.withNoSiteArea) {
      filters.siteAreaID = null;
    } else if (!Utils.isEmptyArray(params.siteAreaIDs)) {
      filters.siteAreaID = {
        $in: params.siteAreaIDs.map((id) => DatabaseUtils.convertToObjectID(id))
      };
    }
    if (Utils.objectHasProperty(params, 'issuer') && Utils.isBoolean(params.issuer)) {
      filters.issuer = params.issuer;
    }
    if (!Utils.isEmptyArray(params.siteIDs)) {
      filters.siteID = {
        $in: params.siteIDs.map((siteID) => DatabaseUtils.convertToObjectID(siteID))
      };
    }
    if (params.dynamicOnly && Utils.isBoolean(params.dynamicOnly)) {
      filters.dynamicAsset = params.dynamicOnly;
    }
    if (!Utils.isEmptyArray(params.assetIDs)) {
      filters._id = {
        $in: params.assetIDs.map((assetID) => DatabaseUtils.convertToObjectID(assetID))
      };
    }

    if (!Utils.isEmptyJSon(filters)) {
      aggregation.push({ $match: filters });
    }

    if (!dbParams.onlyRecordCount) {
      aggregation.push({ $limit: Constants.DB_RECORD_COUNT_CEIL });
    }

    const assetsCountMDB = await global.database.getCollection<any>(tenant.id, 'smartmeters')
      .aggregate([...aggregation, { $count: 'count' }], DatabaseUtils.buildAggregateOptions())
      .toArray() as DatabaseCount[];

    if (dbParams.onlyRecordCount) {
      await Logging.traceDatabaseRequestEnd(tenant, MODULE_NAME, 'getsmartMeterAssets', startTime, aggregation, assetsCountMDB);
      return {
        count: (assetsCountMDB.length > 0 ? assetsCountMDB[0].count : 0),
        result: []
      };
    }

    aggregation.pop();
    if (!dbParams.sort) {
      dbParams.sort = { name: 1 };
    }
    aggregation.push({ $sort: dbParams.sort });
    if (dbParams.skip > 0) {
      aggregation.push({ $skip: dbParams.skip });
    }
    aggregation.push({ $limit: dbParams.limit });

    if (params.withSiteArea) {
      DatabaseUtils.pushSiteAreaLookupInAggregation({
        tenantID: tenant.id, aggregation, localField: 'siteAreaID', foreignField: '_id',
        asField: 'siteArea', oneToOneCardinality: true
      });
    }

    if (params.withSite) {
      DatabaseUtils.pushSiteLookupInAggregation({
        tenantID: tenant.id, aggregation, localField: 'siteID', foreignField: '_id',
        asField: 'site', oneToOneCardinality: true
      });
    }

    DatabaseUtils.pushRenameDatabaseID(aggregation);
    DatabaseUtils.pushConvertObjectIDToString(aggregation, 'siteAreaID');
    DatabaseUtils.pushConvertObjectIDToString(aggregation, 'siteArea.siteID');
    DatabaseUtils.pushCreatedLastChangedInAggregation(tenant.id, aggregation);
    DatabaseUtils.projectFields(aggregation, projectFields);

    const assetsMDB = await global.database.getCollection<any>(tenant.id, 'smartmeters')
      .aggregate<any>(aggregation, DatabaseUtils.buildAggregateOptions())
      .toArray() as Asset[];

    await Logging.traceDatabaseRequestEnd(tenant, MODULE_NAME, 'getsmartMeterAssets', startTime, aggregation, assetsMDB);
    return {
      count: DatabaseUtils.getCountFromDatabaseCount(assetsCountMDB[0]),
      result: assetsMDB
    };
  }

  public static async deleteAsset(tenant: Tenant, id: string): Promise<void> {
    const startTime = Logging.traceDatabaseRequestStart();
    DatabaseUtils.checkTenantObject(tenant);
    await global.database.getCollection<any>(tenant.id, 'smartmeters')
      .findOneAndDelete({ '_id': DatabaseUtils.convertToObjectID(id) });
    await global.database.getCollection<any>(tenant.id, 'assetimages')
      .findOneAndDelete({ '_id': DatabaseUtils.convertToObjectID(id) });
    await Logging.traceDatabaseRequestEnd(tenant, MODULE_NAME, 'deleteAsset', startTime, { id });
  }

  private static async saveAssetImage(tenant: Tenant, assetID: string, assetImageToSave: string): Promise<void> {
    const startTime = Logging.traceDatabaseRequestStart();
    DatabaseUtils.checkTenantObject(tenant);
    await global.database.getCollection<any>(tenant.id, 'assetimages').findOneAndUpdate(
      { '_id': DatabaseUtils.convertToObjectID(assetID) },
      { $set: { image: assetImageToSave } },
      { upsert: true });
    await Logging.traceDatabaseRequestEnd(tenant, MODULE_NAME, 'saveAssetImage', startTime, assetImageToSave);
  }
}


