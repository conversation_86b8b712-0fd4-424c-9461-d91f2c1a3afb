{"$id": "setting", "definitions": {"id": {"$ref": "common#/definitions/id"}, "identifier": {"type": "string", "sanitize": "mongo"}, "sensitiveData": {"type": "array", "nullable": true, "items": {"type": "string"}, "sanitize": "mongo"}, "links": {"type": "array", "items": {"type": "object", "properties": {"id": {"$ref": "common#/definitions/string-id"}, "name": {"type": "string", "sanitize": "mongo"}, "description": {"type": "string", "sanitize": "mongo"}, "role": {"type": "string", "sanitize": "mongo"}, "url": {"type": "string", "sanitize": "mongo"}}}}}}