{"$id": "chargingstation", "definitions": {"id": {"$ref": "common#/definitions/string-id"}, "connector": {"type": "object", "properties": {"connectorId": {"type": "number", "sanitize": "mongo"}, "type": {"type": "string", "sanitize": "mongo", "enum": ["T2", "CCS", "C", "T1", "T3C", "T1CCS", "D", "U", "GTA", "GTD", "T"]}, "power": {"type": "number", "sanitize": "mongo"}, "voltage": {"type": "integer", "enum": [110, 230], "sanitize": "mongo"}, "amperage": {"type": "number", "sanitize": "mongo"}, "currentType": {"type": "string", "enum": ["AC", "DC"], "sanitize": "mongo"}, "numberOfConnectedPhase": {"type": "number", "sanitize": "mongo"}, "phaseAssignmentToGrid": {"type": "object", "properties": {"csPhaseL1": {"type": "string", "sanitize": "mongo"}, "csPhaseL2": {"type": "string", "sanitize": "mongo"}, "csPhaseL3": {"type": "string", "sanitize": "mongo"}}}, "tariffID": {"$ref": "common#/definitions/tariff-id"}, "connectID": {"type": "string", "sanitize": "mongo", "pattern": "^([a-zA-Z0-9-]{3,5})?$"}}, "required": ["connectorId"]}, "chargePoint": {"type": "object", "properties": {"chargePointID": {"type": "number", "sanitize": "mongo"}, "currentType": {"type": "string", "enum": ["AC", "DC"], "sanitize": "mongo"}, "voltage": {"type": "number", "enum": [110, 230], "sanitize": "mongo"}, "amperage": {"type": "number", "sanitize": "mongo"}, "numberOfConnectedPhase": {"type": "number", "sanitize": "mongo"}, "cannotChargeInParallel": {"type": "boolean", "sanitize": "mongo"}, "sharePowerToAllConnectors": {"type": "boolean", "sanitize": "mongo"}, "excludeFromPowerLimitation": {"type": "boolean", "sanitize": "mongo"}, "ocppParamForPowerLimitation": {"type": "string", "sanitize": "mongo"}, "power": {"type": "number", "sanitize": "mongo"}, "efficiency": {"type": "number", "sanitize": "mongo"}, "connectorIDs": {"type": "array", "items": {"$ref": "#/definitions/connector/properties/connectorId"}}}, "required": ["chargePointID"]}, "remarks": {"type": "string", "sanitize": "mongo"}}}