{"billing": {"generatedUser": "<PERSON><PERSON><PERSON><PERSON> Nutzer für '{{email}}'", "chargingAtSiteArea": "Ladevorgang: {{sessionID}} - Startdatum: {{startDate}} um {startTime} - Energieverbrauch: {{totalConsumption}} kWh bei {{siteAreaName}}", "chargingAtChargeBox": "Ladevorgang: {{sessionID}} - Startdatum: {{startDate}} um {{startTime}} - Energieverbrauch: {{totalConsumption}} kWh an Ladestation {{chargeBoxID}}", "header-itemDescription": "Session: {{sessionID}} - Startdatum: {{startDate}} um {{startTime}} an Ladestation {{chargeBoxID}}", "flatFee-shortItemDescription": "Pauschalgebühr", "energy-shortItemDescription": "Energieverbrauch: {{quantity}} kWh", "chargingTime-shortItemDescription": "Ladezeit: {{duration}}", "parkingTime-shortItemDescription": "Parkzeit: {{duration}}", "transfer-feeItemDescription": "Gebühr erhoben für {{nbSessions}} Ladevorgänge"}, "chargers": {"chargeBoxSN": "Seriennummer Ladestation", "chargePointSN": "Seriennummer Ladepunkt", "chargingStation": "Ladestation", "connector": "<PERSON><PERSON><PERSON><PERSON>", "firmwareVersion": "Firmware-Version", "lastReboot": "Letzter Neustart", "lastSeen": "Letzte Vebindung", "maxPower": "Maxim<PERSON> (Watt)", "model": "Model", "numberOfConnectors": "Anzahl der Anschlüsse", "ocppProtocol": "OCPP-Protokoll", "ocppVersion": "OCPP-Version", "powerLimitUnit": "Leistungseinheit des Limits", "timezone": "Zeitzone der Ladestation", "vendor": "<PERSON><PERSON><PERSON>"}, "general": {"changedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON> von", "changedOn": "Geändert am", "createdOn": "Erstellt am", "date": "Datum", "endDate": "Enddatum", "endTime": "Endzeit", "invalidDate": "Ungültiges Datum", "invalidTime": "Ungültige Zeit", "latitude": "Breitengrad", "longitude": "Längengrad", "month": "<PERSON><PERSON>", "name": "Name", "price": "Pre<PERSON>", "priceUnit": "Preiseinheit", "site": "<PERSON><PERSON>", "siteArea": "Standortbereich", "startDate": "Startdatum", "startTime": "Startzeit", "time": "Zeit", "value": "Wert", "year": "<PERSON><PERSON><PERSON>"}, "loggings": {"action": "Aktion", "host": "Host", "level": "Level", "message": "Nachricht", "method": "<PERSON>e", "module": "<PERSON><PERSON><PERSON>", "process": "Prozess", "source": "Ursprung", "type": "<PERSON><PERSON>"}, "notifications": {"sessionNotStarted": {"title": "Der Ladevorgang wurde nicht gestartet", "body": "Sie haben sich an der Ladestation '{{chargeBoxID}}' authentifiziert, aber es konnte kein Ladevorgang gestartet werden"}, "sessionStarted": {"title": "Ladevorgang gestartet", "body": "<PERSON>hr Ladevorgang an der Ladestation '{{chargeBoxID}}', Ladepunkt '{{connectorId}}' wurde erfolgreich gestartet. Organsation: '{{tenantName}}'"}, "ocpiPatchChargingStationsStatusesError": {"title": "Senden des Roaming Status", "body": "Der Status der Ladestation konnte nicht zur Roaming-Plattform übermittelt werden. Ort: '{{location}}' Organisation: '{{tenantName}}', überprüfen Sie die Logs"}, "oicpPatchChargingStationsStatusesError": {"title": "Senden des Roaming Status", "body": "Der Status der Ladestationen konnte nicht zur Hubject Roaming-Plattform übermittelt werden. Organisation: '{{tenantName}}', überprüfen Sie die Logs"}, "oicpPatchChargingStationsError": {"title": "Senden der Hubject EVSE Daten", "body": "Die EVSE Daten der Ladestationen konnte nicht zur Hubject Roaming-Plattform übermittelt werden. Organisation: '{{tenantName}}', überprüfen Sie die Logs"}, "optimalChargeReached": {"title": "Optimale Ladung erreicht", "body": "<PERSON><PERSON> Elektroauto, verbunden mit Ladestation '{{chargeBoxID}}', Ladepunkt '{{connectorId}}', hat die optimale Ladung (85%) erreicht. Organisation: '{{tenantName}}'"}, "endOfCharge": {"title": "Laden abgeschlossen", "body": "<PERSON><PERSON> Elektroauto, verbunden mit Ladestation '{{chargeBoxID}}', Ladepunkt '{{connectorId}}', hat gerade das Laden abgeschlossen. Organisation: '{{tenantName}}'"}, "endOfSession": {"title": "Ladevorgang abgeschlossen", "body": "<PERSON>hr Ladevorgang an der Ladestation '{{chargeBoxID}}', Ladepunkt '{{connectorId}}' wurde gerade abgeschlossen. Organisation: '{{tenantName}}'"}, "chargingStationStatusError": {"title": "Fehler an Ladestation", "body": "<PERSON><PERSON> aufgetreten an Ladestation '{{chargeBoxID}}', Ladepunkt '{{connectorId}}'. Organisation: '{{tenantName}}': {{error}}"}, "unknownUserBadged": {"title": "<PERSON><PERSON><PERSON><PERSON>", "body": "Ein unbekannter Benutzer hat sich an Ladestation '{{chargeBoxID}}' mit der ID '{{badgeID}}' authentifiziert. Organisation: '{{tenantName}}'"}, "chargingStationRegistered": {"title": "Ladestation verbunden", "body": "Die Ladestation '{{chargeBoxID}}' wurde gerade mit dem zentralen Server verbunden. Organisation: '{{tenantName}}'"}, "userAccountStatusChanged": {"title": "Konto {{status}}", "activated": "aktiviert", "suspended": "<PERSON>ak<PERSON><PERSON><PERSON>", "body": "<PERSON><PERSON> Konto wurde von e<PERSON>m Administrator {{status}}. Organisation: '{{tenantName}}'"}, "userWalletMinimumAmtReached": {"title": "Wallet Balance Reached Minimum Amount", "body": "You Wallet balance has been reached minimum Amout. Please recharge your wallet and continue charging. "}, "userAccountInactivity": {"title": "Benutzerkonto", "body": "<PERSON>hr Benutzerkonto ist inaktiv seit {{lastLogin}}. Organisation: '{{tenantName}}'. Bitte nehmen Sie zur Kenntnis, dass aus Compliance Gründen Benutzerkonten nach 6 monatiger Inaktivität gelöscht werden."}, "preparingSessionNotStarted": {"title": "Ladevorgang nicht gestartet", "body": "<PERSON> Ladevorgang an Ladestation '{{chargeBoxID}}', Ladepunkt '{{connectorId}}' wurde nicht gestartet. Organisation: '{{tenantName}}'"}, "offlineChargingStation": {"title": "Ladestation offline", "body": "Folgende Ladestationen sind offline. Organisation: '{{tenantName}}': {{chargeBoxIDs}}"}, "billingUserSynchronizationFailed": {"title": "Benutzersynchronisierung fehlgeschlagen", "body": "Die Synchronisierung von {{nbrUsersInError}} <PERSON><PERSON><PERSON>(n)  mit dem Bezahldienstleister ist fehlgeschlagen. Organisation: '{{tenantName}}'"}, "billingInvoiceSynchronizationFailed": {"title": "Rechnungssynchronisierung fehlgeschlagen", "body": "Die Synchronisierung von {{nbrInvoicesInError}} Rechnung(en) mit dem Abrechnungsdienstleister ist fehlgeschlagen. Organisation: '{{tenantName}}'"}, "billingPeriodicOperationFailed": {"title": "Periodische Abrechnung", "body": "Verarbeitung von {{nbrInvoicesInError}} Rechnungen. Organisation '{{tenantName}}'"}, "computeAndApplyChargingProfilesFailed": {"title": "Ladeprofile fehlgeschlagen", "body": "Es war nicht möglich die Ladeprofile für '{{chargeBoxID}}' im Standortbereich '{{siteAreaName}}' in der Organisation '{{tenantName}}' anzuwenden"}, "billingNewInvoicePaid": {"title": "Neue beglichene Rechnung", "body": "Ihre Rechnung ({{invoiceNumber}}) in <PERSON><PERSON><PERSON> von {{amount}} wurde beglichen und ist nun verfügbar"}, "billingNewInvoiceOpen": {"title": "Neue offene Rechnung", "body": "Eine neue Rechnung ({{invoiceNumber}}) in <PERSON><PERSON><PERSON> von {{amount}} ist jetzt verfügbar. Bitte prüfen Sie die E-Mail, um die Zahlung abzuschließen"}, "billingAccountCreationLink": {"title": "Einrichtung Abrechnungskonto", "body": "<PERSON><PERSON> wurden e<PERSON>n, ein Abrechnungskonto zu erstellen. Bitte prüfen Sie Ihre E-Mails, um den Vorgang abzuschließen"}, "billingAccountActivated": {"title": "Abrechnungskonto aktiviert", "body": "Ihr Abrechnungskonto wurde aktiviert"}, "endUserErrorNotification": {"title": "Ein Nutzer hat einen Fehler gemeldet", "body": "'{{userName}}' hat foglenden <PERSON>: '{{errorTitle}}' - '{{errorDescription}}' in Organisaion '{{tenantName}}' gemeldet"}, "accountVerificationNotification": {"title": "Benutzerkonto verifiziert", "bodyVerified": "Ihr Benutzerkonto wurde erfolgreich verifiziert. Ein Administrator wird es prüfen und freischalten", "bodyVerifiedAndActivated": "Ihr Benutzerkonto wurde erfolgreich verifiziert und aktiviert"}}, "siteArea": "Standortbereich", "statistics": {"consumption": "Consumption (kW.h)", "inactivity": "Inaktivität (Stunden)", "numberOfSessions": "Anzahl der Ladevorgänge", "usage": "Nutzung (Stunden)"}, "tags": {"id": "RFID", "description": "Badge Beschreibung", "virtualBadge": "Virtueller Badge"}, "transactions": {"totalConsumption": "Gesamtverbrauch (kW.h)", "totalDuration": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Mins)", "totalInactivity": "Gesamtinaktivität (Mins)"}, "users": {"email": "Email", "eulaAcceptedOn": "EULA akzeptiert am", "firstName": "<PERSON><PERSON><PERSON>", "id": "ID", "role": "<PERSON><PERSON>", "status": "Status", "user": "<PERSON><PERSON><PERSON>", "userID": "Nutzer ID"}, "email": {"greetings": "Hallo {{recipientName}}", "footerText": "<PERSON><PERSON> Nachricht wurde von einem automatischen E-Mail-Dienst gesendet an {{recipientEmail}}. Bitte antworten Sie nicht auf diese E-Mail. Sie können sich vom Dienst abmelden, indem Sie Ihre Benachrichtigungseinstellungen anpassen.", "new-registered-user": {"title": "Benutzerkonto Erstellt", "buttonText": "Aktivieren Sie Ihr Benutzerkonto", "text": ["<PERSON>hr Benutzerkonto wurde erfolgreich erstellt.", "", "Folgen Sie dem unten stehenden Link, um es zu aktivieren."]}, "request-password": {"title": "Passwort Zurücksetzen", "buttonText": "Passwort zurücksetzen", "text": ["Sie haben beantragt Ihr Passwort zurückzusetzen.", "", "Folgen Sie dem unten stehenden Link, um ein neues Passwort zu vergeben."]}, "optimal-charge-reached": {"title": "Optimaler Ladestand Erreicht", "buttonText": "Ladevorgang anzeigen", "text": ["Ihr Fahrzeug verbunden mit {{chargeBoxID}}, Ladepunkt {{connectorId}}, hat den optimalen Ladestand erreicht."], "table": [{"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "{{transactionId}}"}, {"label": "Batteriestand", "value": "{{stateOfCharge}} %"}]}, "end-of-session": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "buttonText": "Ladevorgang anzeigen", "text": ["<PERSON><PERSON> Ladevorgang an Ladestation {{chargeBoxID}}, Ladepunkt {{connectorId}} wurde beendet."], "table": [{"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "{{transactionId}}"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "{{totalConsumption}} kW.h"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "{{totalDuration}}"}, {"label": "Gesamtinaktivität", "value": "{{totalInactivity}}"}, {"label": "Batteriestand", "value": "{{stateOfCharge}} %"}]}, "end-of-signed-session": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "buttonText": "Ladevorgang anzeigen", "text": ["Ladetransaktion abgeschlossen"], "table": [{"label": "Ladestation", "value": "{{chargeBoxID}}"}, {"label": "Ladepunkt", "value": "{{connectorId}}"}, {"label": "RFID", "value": "{{tagId}}"}, {"label": "Startzeit", "value": "{{startDate}}"}, {"label": "Endzeit", "value": "{{endDate}}"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "{{meterStart}} kWh"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "{{meterStop}} kWh"}, {"label": "Consumption", "value": "{{totalConsumption}} kWh"}, {"label": "<PERSON><PERSON>", "value": "{{price}}€ {{relativeCost}}€/kWh) (informativ)"}, {"label": "Vertrag für", "value": "LOCAL (informativ)"}]}, "end-of-charge": {"title": "<PERSON><PERSON>", "buttonText": "Ladevorgang anzeigen", "text": ["<PERSON><PERSON> Fahrzeug, verbunden mit Ladestation {{chargeBoxID}}, Ladepunkt {{connectorId}}, hat das Laden beendet."], "table": [{"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "{{transactionId}}"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "{{totalConsumption}} kW.h"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "{{totalDuration}}"}, {"label": "Batteriestand", "value": "{{stateOfCharge}} %"}]}, "billing-new-invoice-paid": {"title": "Abrechnung - Bezahlte Rechnung", "buttonText": "Rechnung herunterladen", "text": ["Ihre Rechnung {{invoiceNumber}} wurde bezahlt und kann nun heruntergeladen werden."], "table": [{"label": "Gezahlter Betrag", "value": "{{invoiceAmount}}"}]}, "billing-new-invoice-unpaid": {"title": "Abrechnung - Neue Rechnung", "buttonText": "<PERSON><PERSON><PERSON><PERSON> bezahlen", "text": ["die Rechnung {{invoiceNumber}} wurde erstellt und kann nun heruntergeladen werden.", "", "Bitte folgen Sie dem unten stehenden Link, um die Zahlung abzuschließen."], "table": [{"label": "<PERSON><PERSON><PERSON><PERSON> Betrag", "value": "{{invoiceAmount}}"}]}, "charging-station-registered": {"title": "Ladestation Verbunden", "buttonText": "Ladestation anzeigen", "text": ["die Ladestation {{chargeBoxID}} wurde mit dem Server verbunden."]}, "end-user-error-notification": {"title": "Ein Nutzer hat einen Fehler gemeldet", "buttonText": "Zu {{tenant<PERSON>ame}} navigieren", "text": ["der Nutzer {{name}} hat den folgenden Fehler gemeldet:", "", "<PERSON><PERSON>: {{errorTitle}}", "Beschreibung: {{errorDescription}}", "", "E-Mail: {{email}}", "Telefon: {{phone}}"]}, "offline-charging-station": {"title": "Offline-Ladestationen", "buttonText": "Zu {{tenant<PERSON>ame}} navigieren", "text": ["in Ihrer Organisation sind {{nbChargingStationIDs}} Ladestationen offline.", "", "Ladestationen: {{tenFirstChargingStationIDs}}"]}, "charging-station-status-error": {"title": "Ladestation - <PERSON><PERSON> an {{chargeBoxID}}, Ladepunkt {{connectorId}}", "buttonText": "Fehler anzeigen", "text": ["an {{chargeBoxID}}, Ladepunkt {{connectorId}} ist ein <PERSON>hler aufgetreten.", "<PERSON><PERSON>: {{error}}"]}, "billing-account-created": {"title": "Abrechnungskonto Einrichten", "buttonText": "Fortfahren", "text": ["ein Abrechungskonto wurde für Sie von einem Adimnistrator erstellt.", "", "Bitte folgen Sie dem unten stehenden Link, um die Einrichtung abzuschließen."]}, "user-account-status-changed": {"title": "Benutzerkonto - Status geändert", "buttonText": "Zu {{tenant<PERSON>ame}} navigieren", "text": ["der Status Ihres Benutzerkontos wurde von einem Administrator geändert.", "Neuer Status: {{accountStatus}}"]}, "unknown-user-badged": {"title": "Authentifizierungsversuch - Unbekannter Nutzer", "buttonText": "Zu {{tenant<PERSON>ame}} navigieren", "text": ["ein unbekannter <PERSON> hat sich an {{chargeBoxID}} mit der RFID-Karte {{badgeID}} authentifiziert."]}, "session-started": {"title": "Ladevorgan<PERSON>", "buttonText": "Ladevorgang anzeigen", "text": ["<PERSON>hr Fahrzeug wurde mit der Ladestation {{chargeBoxID}} an Ladepunkt {{connectorId}} verbunden."]}, "verification-email": {"title": "Benutzerkonto Aktivieren", "buttonText": "Aktivieren Sie Ihr Benutzerkonto", "text": ["Sie haben eine Anfrage zur Aktivierung Ihres Benutzerkontos gestellt.", "", "Bitte folgen Sie dem unten stehenden Link, um die Aktivierung abzuschließen."]}, "verification-email-user-import": {"title": " Benutzerkonto Erstellt", "buttonText": "Aktivieren Sie Ihr Benutzerkonto", "text": ["ein Benutzerkonto wurde für Sie erstellt.", "", "Bitte folgen Sie dem unten stehenden Link, um die Aktivierung abzuschließen."]}, "ocpi-patch-status-error": {"title": "Übertragung der Status der Ladestation fehlgeschlagen", "buttonText": "Zu {{tenant<PERSON>ame}} navigieren", "text": ["die OCPI-Übertragung der Ladestation-Status ist für den Standort {{location}} fehlgeschlagen.", "", "Bitte überprüfen Sie die Logs."]}, "oicp-patch-status-error": {"title": "Hubject - Status senden", "buttonText": "Zu {{tenant<PERSON>ame}} navigieren", "text": ["die Übertragung der Ladestation-Status zur Hubject-Roaming-Plattform für die Organisation '{{tenantName}}' ist fehlgeschlagen.", "", "Bitte überprüfen Sie die Logs."]}, "oicp-patch-evses-error": {"title": "Hubject - Ladestationen senden", "buttonText": "Zu {{tenant<PERSON>ame}} navigieren", "text": ["die Übertragung der Ladestationsdaten zur Hubject-Roaming-Plattform für die Organisation '{{tenantName}}' ist fehlgeschlagen.", "", "Bitte überprüfen Sie die Logs."]}, "user-account-inactivity": {"title": "Benutzerkonto - Inaktiv", "buttonText": "Zu {{tenant<PERSON>ame}} navigieren", "text": ["Sie haben sich seit {{lastLogin}} nicht bei e-mobility angemeldet.", "<PERSON><PERSON> Si<PERSON>, dass Ihr Benutzerkonto von einem Administrator gelöscht werden kann, um die allgemeine Datenschutzverordnung einzuhalten.", "", "Bitte melden Si<PERSON> sich an und akzeptieren Sie die aktuelle Endbenutzer-Lizenzvereinbarung, um die Löschung Ihres Kontos zu verhindern."]}, "session-not-started": {"title": "<PERSON><PERSON>", "buttonText": "Ladestation anzeigen", "text": ["Sie haben den Ladevorgang an Ladestation {{chargeBoxID}}, Ladepunkt {{connectorId}} nicht gestartet."]}, "session-not-started-after-authorize": {"title": "<PERSON><PERSON> gesta<PERSON>t", "buttonText": "Ladestation anzeigen", "text": ["Sie haben sich an der Ladestation {{chargeBoxID}} authentifiziert, aber es wurde kein Ladevorgang gestartet."]}, "billing-user-synchronization-failed": {"title": "Abrechnung - Nutzersynchronisierung fehlgeschlagen", "buttonText": "Zu {{tenant<PERSON>ame}} navigieren", "text": ["es war nicht möglich {{nbrUsersInError}} Nutzer mit dem Abrechnungsdienstleister zu synchronisieren."]}, "billing-invoice-synchronization-failed": {"title": "Abrechnung - Rechnungssynchronisierung fehlgeschlagen", "buttonText": "Zu {{tenant<PERSON>ame}} navigieren", "text": ["es war nicht möglich {{nbrInvoicesInError}} Rechnungen mit dem Abrechnungsdienstleister zu synchronisieren."]}, "billing-periodic-operation-failed": {"title": "Abrechnung - Periodische Durchführung Fehlgeschlagen", "buttonText": "Zu {{tenant<PERSON>ame}} navigieren", "text": ["es war nicht möglich {{nbrInvoicesInError}} Rechnungen zu verarbeiten."]}, "billing-account-activated": {"title": "Abrechnungskonto aktiviert", "buttonText": "Zu {{tenant<PERSON>ame}} navigieren", "text": ["Herzlichen Glückwunsch!", "", "Der Einrichtungsprozess wurde erfolgreich beendet und Ihr Abrechnungskonto erfolgreich aktiviert."]}, "car-synchronization-failed": {"title": "Fahrzeug - Synchronisierung fehlgeschlagen", "buttonText": "Zu {{tenant<PERSON>ame}} navigieren", "text": ["es war nicht möglich {{nbrCarsInError}} Fahrzeuge mit dem Datenanbieter für E-Fahrzeuge zu synchronisieren."]}, "compute-and-apply-charging-profiles-failed": {"title": "Anwenden der Ladeprofile fehlgeschlagen", "buttonText": "Zu {{tenant<PERSON>ame}} navigieren", "text": ["anwenden der Ladeprofile für {{chargeBoxID}} ist fehlgeschlagen.", "Die Ladestation wurde automatisch von der aktuellen Smart Charging-Iteration ausgeschlossen."]}, "account-verification-notification-active": {"title": "Benutzerkonto verifiziert", "buttonText": "Zu {{tenant<PERSON>ame}} navigieren", "text": ["Ihr Benutzerkonto wurde verifiziert und erfolgreich aktiviert."]}, "account-verification-notification-inactive": {"title": "Benutzerkonto verifiziert", "buttonText": "Zu {{tenant<PERSON>ame}} navigieren", "text": ["Ihre E-Mail wurde erfolgreich verifiziert.", "", "Ein Administrator wird Ihr Benutzerkonto prüfen und es aktivieren."]}, "admin-account-verification-notification": {"title": "Benutzerkonto - Verifizierung", "buttonText": "Benutzerkonto verifizieren", "text": ["ein neuer Nutzer mit der E-Mail {{email}} hat ein Benutzerkonto erstellt.", "", "<PERSON>te folgen Sie dem unten stehenden Link, um es zu aktivieren."]}, "user-create-password": {"title": "Benutzerkonto Erstellt", "buttonText": "Passwort festlegen", "text": ["es wurde ein Benutzerkonto für Sie in Organisation '{{tenantName}}' erstellt.", "Um Ihr Fahrzeug laden zu könne<PERSON>, müssen <PERSON> sich mindestens einmal anmelden.", "", "<PERSON>te folgen Sie dem unten stehenden Link, um ein Passwort festzulegen."]}}}