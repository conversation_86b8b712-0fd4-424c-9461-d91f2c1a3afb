import { ServerAction, SmartMeterRoutes } from '../../../../../types/Server';
import express, { NextFunction, Request, Response } from 'express';
import RouterUtils from '../../../../../utils/RouterUtils';
import SmartMeterService from '../../service/SmartMeterService';

export default class SmartMeterRouter {
  private router: express.Router;

  public constructor() {
    this.router = express.Router();
  }

  public buildRoutes(): express.Router {
    this.buildRouteCreateSmartMeter();
    this.buildRouteGetSmartMeters();
    this.buildRouteGetSmartMeter();
    this.buildRouteUpdateSmartMeter();
    this.buildRoutetDeleteSmartMeter();
    
    return this.router;
  }

  private buildRouteCreateSmartMeter(): void {
    this.router.post(`/${SmartMeterRoutes.SMART_METER_CREATE}`, (req: Request, res: Response, next: NextFunction) => {
     void RouterUtils.handleRestServerAction(SmartMeterService.handleCreateSmartMeter.bind(this), ServerAction.SMART_METER_CREATE, req, res, next);
    });
  }

  private buildRouteGetSmartMeters(): void {
    this.router.get(`/${SmartMeterRoutes.SMART_METER_GET}`, (req: Request, res: Response, next: NextFunction) => {
      void RouterUtils.handleRestServerAction(SmartMeterService.handleGetSmartMeters.bind(this), ServerAction.SMART_METER_GET, req, res, next);
    });
  }
    private buildRouteGetSmartMeter(): void {
    this.router.get(`/${SmartMeterRoutes.SMART_METER}`, (req: Request, res: Response, next: NextFunction) => {
      req.query.ID = req.params.id;
      void RouterUtils.handleRestServerAction(SmartMeterService.handleGetSmartMeter.bind(this), ServerAction. SMART_METER_GET_BY_ID, req, res, next);
    });
  }

  private buildRouteUpdateSmartMeter(): void {
    this.router.put(`/${SmartMeterRoutes.SMART_METER_UPDATE}`, (req: Request, res: Response, next: NextFunction) => {
      req.body.id = req.params.id;
      void RouterUtils.handleRestServerAction(SmartMeterService.handleUpdateSmartMeter.bind(this), ServerAction.SMART_METER_UPDATE, req, res, next);
    });
  }

  private buildRoutetDeleteSmartMeter(): void {
    this.router.delete(`/${SmartMeterRoutes.SMART_METER_DELETE}`, (req: Request, res: Response, next: NextFunction) => {
      req.query.ID = req.params.id;
      void RouterUtils.handleRestServerAction(SmartMeterService.handledeleteSmartMeter.bind(this), ServerAction.SMART_METER_DELETE, req, res, next);
    });
  }
}