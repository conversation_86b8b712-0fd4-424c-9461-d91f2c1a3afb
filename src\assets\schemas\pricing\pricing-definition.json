{"$id": "pricing-definition", "definitions": {"id": {"$ref": "common#/definitions/string-id"}, "entityID": {"type": "string", "sanitize": "mongo"}, "entityType": {"type": "string", "sanitize": "mongo", "enum": ["Tenant", "Company", "Site", "SiteArea", "Corporate", "ChargingStation"]}, "name": {"type": "string", "sanitize": "mongo"}, "description": {"type": "string", "sanitize": "mongo"}, "dayOfWeek": {"type": "number", "sanitize": "mongo", "enum": [1, 2, 3, 4, 5, 6, 7]}, "staticRestrictions": {"type": "object", "nullable": true, "properties": {"validFrom": {"type": "string", "format": "date-time", "customType": "date", "sanitize": "mongo", "nullable": true}, "validTo": {"type": "string", "format": "date-time", "customType": "date", "sanitize": "mongo", "nullable": true}, "connectorType": {"type": "string", "sanitize": "mongo", "enum": ["T2", "CCS", "C", "T1", "T3C", "T1CCS", "D", "U", null], "nullable": true}, "connectorPowerkW": {"type": "number", "sanitize": "mongo"}}}, "restrictions": {"type": "object", "nullable": true, "properties": {"daysOfWeek": {"type": "array", "items": {"$ref": "pricing-definition#/definitions/dayOfWeek"}, "nullable": true}, "timeFrom": {"type": "string", "sanitize": "mongo", "pattern": "^(0[0-9]|1[0-9]|2[0-3]):[0-5][0-9]$"}, "timeTo": {"type": "string", "sanitize": "mongo", "pattern": "^(0[0-9]|1[0-9]|2[0-3]):[0-5][0-9]$"}, "minEnergyKWh": {"type": "number", "sanitize": "mongo"}, "maxEnergyKWh": {"type": "number", "sanitize": "mongo"}, "minDurationSecs": {"type": "number", "sanitize": "mongo"}, "maxDurationSecs": {"type": "number", "sanitize": "mongo"}}}, "pricingDimension": {"type": "object", "nullable": true, "properties": {"active": {"type": "boolean", "sanitize": "mongo"}, "price": {"type": "number", "sanitize": "mongo"}, "stepSize": {"type": "number", "sanitize": "mongo"}, "pricedData": {"type": "object", "properties": {"unitPrice": {"type": "number", "sanitize": "mongo"}, "amount": {"type": "number", "sanitize": "mongo"}, "roundedAmount": {"type": "number", "sanitize": "mongo"}, "quantity": {"type": "number", "sanitize": "mongo"}, "sourceName": {"type": "string", "sanitize": "mongo"}, "itemDescription": {"type": "string", "sanitize": "mongo"}, "taxes": {"type": "array", "items": {"type": "string", "sanitize": "mongo"}}}, "required": ["unitPrice", "amount", "roundedAmount", "quantity"]}}, "required": ["active"]}}}