{"$id": "stop-transaction-request-16", "description": "StopTransactionRequest 1.6", "type": "object", "properties": {"transactionId": {"type": "integer", "description": "number defining the transaction identifier"}, "idTag": {"type": "string", "description": "string defining identification token, e.g. RFID or credit card number. To be treated as case insensitive.", "minLength": 0, "maxLength": 20, "transform": ["toUpperCase"]}, "timestamp": {"type": "string", "format": "date-time", "customType": "date", "description": "string defining the status notification timestamp"}, "meterStop": {"type": "number", "description": "number defining the meter stop of the transaction"}, "reason": {"type": "string", "description": "string defining the reason for stopping the transaction", "enum": ["EmergencyStop", "EVDisconnected", "HardReset", "Local", "Other", "PowerLoss", "Reboot", "Remote", "SoftReset", "UnlockCommand", "DeAuthorized"]}, "transactionData": {"type": "array", "description": "defining the transaction data (meter values) associated to the stop transaction", "items": {"type": "object", "properties": {"timestamp": {"type": "string", "format": "date-time", "customType": "date", "description": "defining the timestamp of the meter value"}, "sampledValue": {"type": "array", "description": "defining the SampledValues associated to the meter value", "items": {"type": "object", "properties": {"value": {"type": "string", "description": "string defining the value of the SampledValue"}, "context": {"type": "string", "description": "string defining the context of the SampledValue", "enum": ["Interruption.Begin", "Interruption.End", "Other", "Sample.Clock", "Sample.Periodic", "Transaction.Begin", "Transaction.End", "<PERSON><PERSON>"]}, "format": {"type": "string", "description": "string defining the format of the SampledValue", "enum": ["Raw", "SignedData"]}, "measurand": {"type": "string", "description": "string defining the measurand of the SampledValue", "enum": ["Current.Export", "Current.Import", "Current.Offered", "Energy.Active.Export.Register", "Energy.Active.Import.Register", "Energy.Reactive.Export.Register", "Energy.Reactive.Import.Register", "Energy.Active.Export.Interval", "Energy.Active.Import.Interval", "Energy.Reactive.Export.Interval", "Energy.Reactive.Import.Interval", "Frequency", "Power.Active.Export", "Power.Active.Import", "Power.Factor", "Power.Offered", "Power.Reactive.Export", "Power.Reactive.Import", "RPM", "SoC", "Temperature", "Voltage"]}, "phase": {"type": "string", "description": "string defining the phase of the SampledValue", "enum": ["L1", "L2", "L3", "N", "L1-N", "L2-N", "L3-N", "L1-L2", "L2-L3", "L3-L1"]}, "location": {"type": "string", "description": "string defining the location of the SampledValue", "enum": ["Body", "Cable", "EV", "Inlet", "Outlet"]}, "unit": {"type": "string", "description": "string defining the unit of the SampledValue", "enum": ["<PERSON><PERSON><PERSON>", "Fahrenheit", "Wh", "kWh", "varh", "k<PERSON>h", "W", "kW", "VA", "kVA", "var", "kvar", "A", "V", "K", "Percent"]}}, "required": ["value"]}, "minItems": 1}}, "required": ["timestamp", "sampledValue"]}}, "performanceID": {"type": "string", "description": "performance correlation ID"}}, "required": ["transactionId", "timestamp", "meterStop"]}