{"$id": "smart-meter-create", "title": "Create Smart Meter Request", "type": "object", "properties": {"name": {"$ref": "smartmeter#/definitions/name"}, "siteAreaID": {"$ref": "smartmeter#/definitions/siteAreaID"}, "assetType": {"$ref": "smartmeter#/definitions/assetType"}, "excludeFromSmartCharging": {"$ref": "smartmeter#/definitions/excludeFromSmartCharging"}, "variationThresholdPercent": {"$ref": "smartmeter#/definitions/variationThresholdPercent"}, "fluctuationPercent": {"$ref": "smartmeter#/definitions/fluctuationPercent"}, "staticValueWatt": {"$ref": "smartmeter#/definitions/staticValueWatt"}, "image": {"$ref": "smartmeter#/definitions/image"}, "dynamicAsset": {"$ref": "smartmeter#/definitions/dynamicAsset"}, "usesPushAPI": {"$ref": "smartmeter#/definitions/usesPushAPI"}, "coordinates": {"$ref": "smartmeter#/definitions/coordinates"}, "connectionID": {"$ref": "smartmeter#/definitions/connectionID"}, "meterID": {"$ref": "smartmeter#/definitions/meterID"}, "meterName": {"$ref": "smartmeter#/definitions/meterName"}, "publicKey": {"$ref": "smartmeter#/definitions/publicKey"}, "privateKey": {"$ref": "smartmeter#/definitions/privateKey"}, "url": {"$ref": "smartmeter#/definitions/url"}}, "required": ["name", "meterName", "url"]}