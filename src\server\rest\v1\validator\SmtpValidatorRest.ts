import { SmtpSetting, SmtpSettings } from '../../../../types/Setting';
import Schema from '../../../../types/validator/Schema';
import SchemaValidator from '../../../../validator/SchemaValidator';
import fs from 'fs';
import global from '../../../../types/GlobalType';

export default class SmtpValidatorRest extends SchemaValidator {
  private static instance: SmtpValidatorRest | null = null;
  private smtpSettingUpdate: Schema = JSON.parse(fs.readFileSync(`${global.appRoot}/assets/server/rest/v1/schemas/smtp/smtp-setting-update.json`, 'utf8'));

  private constructor() {
    super('SmtpValidatorRest');
  }

  public static getInstance(): SmtpValidatorRest {
    if (!SmtpValidatorRest.instance) {
      SmtpValidatorRest.instance = new SmtpValidatorRest();
    }
    return SmtpValidatorRest.instance;
  }

  public validateSmtpSettingUpdateReq(data: Record<string, unknown>): SmtpSettings {
    return this.validate(this.smtpSettingUpdate, data);
  }
}
