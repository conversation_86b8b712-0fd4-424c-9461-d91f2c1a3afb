name: Build CI
on:
  push:
    branches:
      - master
      - master-qa
  pull_request:
    branches:
      - master-qa

jobs:
  build:
    runs-on: ubuntu-latest
    concurrency:
      group: build-${{ github.head_ref || github.ref_name }}
      cancel-in-progress: true
    strategy:
      matrix:
        node-version: [16.x]
    steps:
    - uses: actions/checkout@v2
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v2
      with:
        node-version: ${{ matrix.node-version }}
    - name: npm install
      run: npm ci
    - name: npm run build:prod
      run: npm run build:prod

  build-submodules:
    runs-on: ubuntu-latest
    concurrency:
      group: build-sub-modules-${{ github.head_ref || github.ref_name }}
      cancel-in-progress: true
    strategy:
      matrix:
        node-version: [16.x]
    steps:
    - uses: actions/checkout@v2
      with:
        ssh-key: ${{ secrets.ACTION_ACCESS_SUB_MODULES_PRIVATE_REPOS }}
        submodules: true
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v2
      with:
        node-version: ${{ matrix.node-version }}
    - name: npm install
      run: npm ci
    - name: npm build:prod
      run: npm run build:prod

  docker:
    runs-on: ubuntu-latest
    concurrency:
      group: build-docker-${{ github.head_ref || github.ref_name }}
      cancel-in-progress: true
    steps:
    - uses: actions/checkout@v2
    - name: Set up Docker Buildx
      id: buildx
      uses: docker/setup-buildx-action@v1
    - name: Build docker image
      run: |
        cd docker
        make server-standalone SUBMODULES_INIT=false
