import { ServerAction ,PagesRoutes } from '../../../../../types/Server';
import express, { NextFunction, Request, Response } from 'express';

import RouterUtils from '../../../../../utils/RouterUtils';
import PagesService from '../../service/PagesService';

export default class PagesRouter {
  private router: express.Router;

  public constructor() {
    this.router = express.Router();
  }
  public buildRoutes(): express.Router {
   // this.buildRouteCreatepages();
    this.buildRouteGetpages();
    this.buildRouteUpdatePages();
   // this.buildRouteSoftDeletePages();

    return this.router;
  }
  private buildRouteCreatepages (): void {
     this.router.post(`/${PagesRoutes.PAGES_CREATE}`, (req: Request, res: Response, next: NextFunction) => {
      void RouterUtils.handleRestServerAction(PagesService.handleCreatepages.bind(this), ServerAction.USERS, req, res, next);
     });
    }

    private buildRouteGetpages (): void {
       this.router.get(`/${PagesRoutes. PAGES_GET}`, (req: Request, res: Response, next: NextFunction) => {
        void RouterUtils.handleRestServerAction(PagesService.handleGetpages.bind(this), ServerAction.USERS, req, res, next);
       });
      }

    private buildRouteUpdatePages(): void {
         this.router.put(`/${PagesRoutes.PAGES_UPDATE}`, (req: Request, res: Response, next: NextFunction) => {
          req.body.id = req.params.id;
          void RouterUtils.handleRestServerAction(PagesService.handleUpdatePages.bind(this), ServerAction.PAGES_UPDATE, req, res, next);
         });
        }

        private buildRouteSoftDeletePages(): void {
             this.router.delete(`/${PagesRoutes.PAGES_SOFT_DELETE}`, (req: Request, res: Response, next: NextFunction) => {
              req.query.ID = req.params.id;
              void RouterUtils.handleRestServerAction(PagesService.handlesoftdeletePages.bind(this), ServerAction.PAGES_DELETE, req, res, next);
             });
            }

}
