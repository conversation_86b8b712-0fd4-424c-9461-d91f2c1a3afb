import SmartMeter from '../Smartmeter';


import HttpByIDRequest from './HttpByIDRequest';
import HttpDatabaseRequest from './HttpDatabaseRequest';

export interface HttpSmartMeterCreateRequest extends SmartMeter {
}
export interface HttpSmartMeterGetRequest extends HttpDatabaseRequest {
  id?: string;
  vendorName?: string;
  smartMeterId?: string;
  smartMeterKey?: string;
  isDeleted?: boolean;
  createdDate?: Date;
  updatedDate?: Date;
  Search?: string;
}


export interface HttpSmartMeterUpdateRequest extends SmartMeter {
}

export interface HttpSmartMeterDeleteRequest extends HttpByIDRequest {
  smartMeterId: string;
}
