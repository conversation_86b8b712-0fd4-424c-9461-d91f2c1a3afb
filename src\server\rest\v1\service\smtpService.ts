import { Action, Entity } from '../../../../types/Authorization';
import { NextFunction, Request, Response } from 'express';
import { SmtpSetting, SmtpSettings, SmtpVendor } from '../../../../types/Setting';
import Configuration from '../../../../utils/Configuration';
import Constants from '../../../../utils/Constants';
import Logging from '../../../../utils/Logging';
import { ServerAction } from '../../../../types/Server';
import SettingStorage from '../../../../storage/mongodb/SettingStorage';
import UtilsService from './UtilsService';
import SmtpValidatorRest from '../validator/SmtpValidatorRest';
import { TenantComponents } from '../../../../types/Tenant';


const MODULE_NAME = 'SmtpService';
const SMTP = Configuration.getSMTPConfig();


export default class SmtpService {


  public static async handleGetSmtpSetting(action: ServerAction, req: Request, res: Response, next: NextFunction): Promise<void> {
    // Check if component is active
    UtilsService.assertComponentIsActiveFromToken(req.user, TenantComponents.SMTP, Action.READ, Entity.SETTING, MODULE_NAME, 'handleGetSmtpSetting');
    const smtpSettings: SmtpSettings = await UtilsService.checkAndGetSmtpSettingAuthorization(req.tenant, req.user, null, Action.READ, action);

    res.json(smtpSettings);
    next();
  }

  public static async handleUpdateSmtpSetting(action: ServerAction, req: Request, res: Response, next: NextFunction): Promise<void> {
    // Check if component is active
    UtilsService.assertComponentIsActiveFromToken(req.user, TenantComponents.SMTP,
      Action.UPDATE, Entity.SETTING, MODULE_NAME, 'handleUpdateSmtpSetting');
      const newSmtpProperties = SmtpValidatorRest.getInstance().validateSmtpSettingUpdateReq({ ...req.params, ...req.body });
      console.log('newSmtpProperties---------->',newSmtpProperties)
    // Check and get previous settings that we want to update
    const smtpSettings = await UtilsService.checkAndGetSmtpSettingAuthorization(req.tenant, req.user, null, Action.UPDATE, action);
    console.log('smtpSettings-------->',smtpSettings)
    let readOnlyProperties = {};
    if (newSmtpProperties.type === SmtpVendor.SMTP) {
      smtpSettings.smtp = {
        ...newSmtpProperties.smtp,
        ...readOnlyProperties
      };
    }
     // Update timestamp
    smtpSettings.lastChangedBy = { 'id': req.user.id };
    smtpSettings.lastChangedOn = new Date();
    // // Let's save the new settings
    await SettingStorage.saveSmtpSetting(req.tenant, smtpSettings);
    // Post-process the activation of the billing feature
    res.json(Constants.REST_RESPONSE_SUCCESS);
    next();
  }
}
