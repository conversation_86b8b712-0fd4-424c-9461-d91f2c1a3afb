{"$id": "wallet", "definitions": {"id": {"$ref": "common#/definitions/id"}, "amount": {"type": "number"}, "userID": {"$ref": "common#/definitions/id"}, "orderId": {"type": "string", "sanitize": "mongo"}, "currency": {"type": "string", "sanitize": "mongo"}, "paymentId": {"type": "string", "sanitize": "mongo"}, "paymentDetails": {"type": "object"}, "orderDetails": {"type": "object", "sanitize": "mongo"}, "remarks": {"type": "string", "sanitize": "mongo"}, "transactionRef": {"type": "string", "sanitize": "mongo"}, "transactionDate": {"type": "string", "format": "date-time", "customType": "date", "sanitize": "mongo"}}}