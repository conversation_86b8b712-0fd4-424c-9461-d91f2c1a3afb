{"$id": "invoicePayment", "definitions": {"id": {"$ref": "common#/definitions/id"}, "paymentAttachmentInvoiceId": {"type": "string", "sanitize": "mongo"}, "amountInvoiceAmount": {"type": "number"}, "transactionDate": {"type": "string", "sanitize": "mongo"}, "paymentMode": {"type": "string", "sanitize": "mongo"}, "settlementDate": {"type": "string", "sanitize": "mongo"}, "paymentReferenceNumberTransactionNo": {"type": "string", "sanitize": "mongo"}, "remarks": {"type": "string", "sanitize": "mongo"}, "corporateID": {"$ref": "common#/definitions/id"}}}