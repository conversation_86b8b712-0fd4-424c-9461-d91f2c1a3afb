name: <PERSON>t <PERSON>I
on:
  # push:
  #   branches:
  #     - master
  #     - master-qa
  pull_request:
    branches:
      - master-qa
jobs:
  lint:
    runs-on: ubuntu-latest
    concurrency:
      group: ${{ github.head_ref || github.ref_name }}
      cancel-in-progress: true
    strategy:
      matrix:
        node-version: [16.x]
    steps:
    - uses: actions/checkout@v2
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v2
      with:
        node-version: ${{ matrix.node-version }}
    - name: npm install
      run: npm ci
    - name: npm run eslint
      run: npm run eslint
