import { RESTServerRoute, ServerAction } from '../../../../../types/Server';
import express, { NextFunction, Request, Response } from 'express';
import RouterUtils from '../../../../../utils/RouterUtils';
import SmtpService  from '../../service/smtpService';

export default class SmtpRouter {
  private router: express.Router;

  public constructor() {
    this.router = express.Router();
  }

  public buildRoutes(): express.Router {
    this.buildRouteSmtpSetting();
    this.buildRouteUpdateSmtpSetting();
    return this.router;
  }

  private buildRouteSmtpSetting(): void {
    this.router.get(`/${RESTServerRoute.REST_SMTP_SETTING}`, (req: Request, res: Response, next: NextFunction) => {
      console.log("handleGetSmtpSetting---------------");
     void RouterUtils.handleRestServerAction(SmtpService.handleGetSmtpSetting.bind(this), ServerAction.SETTINGS, req, res, next);
    });
  }

  private buildRouteUpdateSmtpSetting(): void {
    this.router.put(`/${RESTServerRoute.REST_SMTP_SETTING}`, (req: Request, res: Response, next: NextFunction) => {
      console.log("handleUpdateSmtpSetting-------------");
      void RouterUtils.handleRestServerAction(SmtpService.handleUpdateSmtpSetting.bind(this), ServerAction.SETTINGS, req, res, next);
    });
  }
}
