{"$id": "corporate", "definitions": {"id": {"$ref": "common#/definitions/id"}, "CorporateName": {"type": "string", "sanitize": "mongo"}, "CorporateAddress": {"$ref": "common#/definitions/address"}, "AuthorisedPerson": {"type": "string", "sanitize": "mongo"}, "PAN_TAN": {"type": "string", "sanitize": "mongo"}, "GSTNo": {"type": "string", "sanitize": "mongo"}, "Pricing": {"type": "string", "sanitize": "mongo"}, "Billing": {"type": "string", "sanitize": "mongo", "enum": ["Immediate", "Periodic"]}}}