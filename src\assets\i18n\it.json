{"billing": {"generatedUser": "Utenza generata per '{{email}}'", "chargingAtSiteArea": "Sessione di ricarica: {{sessionID}} - Data di inizio: {{startDate}} alle {startTime} - Consumo di Energia: {{totalConsumption}} kWh a {{siteAreaName}}", "chargingAtChargeBox": "Sessione di ricarica: {{sessionID}} - Data di inizio: {{startDate}} alle {{startTime}} - Consumo di Energia: {{totalConsumption}} kWh alla stazione di ricarica {{chargeBoxID}}", "header-itemDescription": "Sessione: {{sessionID}} - Data di inizio: {{startDate}} alle {{startTime}} alla stazione di ricarica {{chargeBoxID}}", "flatFee-shortItemDescription": "<PERSON><PERSON><PERSON>", "energy-shortItemDescription": "Consumo di Energia: {{quantity}} kWh", "chargingTime-shortItemDescription": "Tempo di Ricarica: {{duration}}", "parkingTime-shortItemDescription": "Tempo di Parcheggio: {{duration}}", "transfer-feeItemDescription": "Tariffa addebitata per {{nbSessiones}} sessione(i)"}, "chargers": {"chargeBoxSN": "Box di Ricarica S/N", "chargePointSN": "Punto di Ricarica S/N", "chargingStation": "Stazione di Ricarica", "connector": "Presa", "firmwareVersion": "Versione del Firmware", "lastReboot": "<PERSON><PERSON><PERSON>", "lastSeen": "Visto l'ultima volta", "maxPower": "<PERSON><PERSON><PERSON> (Watt)", "model": "<PERSON><PERSON>", "numberOfConnectors": "Numero di Connettori", "ocppProtocol": "Protocollo OCPP", "ocppVersion": "Versione OCPP", "powerLimitUnit": "Unità di limitazione della potenza", "timezone": "Fuso Orario della Stazione di Ricarica", "vendor": "Fornitore"}, "general": {"changedBy": "Modificato da", "changedOn": "Modificato il", "createdOn": "Creato il", "date": "Data", "endDate": "Data di fine", "endTime": "<PERSON>a di fine", "invalidDate": "Data non valida", "invalidTime": "Ora non valida", "latitude": "Latitude", "longitude": "Longitude", "month": "Mese", "name": "Nome", "price": "Prezzo", "priceUnit": "Unità di prezzo", "site": "<PERSON><PERSON>", "siteArea": "Area del sito", "startDate": "Data di inizio", "startTime": "Ora di inizio", "time": "<PERSON>a", "value": "Valore", "year": "<PERSON><PERSON>"}, "loggings": {"action": "Azione", "host": "<PERSON><PERSON><PERSON><PERSON>", "level": "<PERSON><PERSON>", "message": "Messaggio", "method": "<PERSON><PERSON><PERSON>", "module": "<PERSON><PERSON><PERSON>", "process": "Processo", "source": "Fonte", "type": "Type"}, "notifications": {"sessionNotStarted": {"title": "Sessione non avviata", "body": "Ti sei registrato sulla stazione di ricarica '{{chargeBoxID}}' ma nessuna sessione è stata avviata"}, "sessionStarted": {"title": "Sessione Avviata", "body": "La tua sessione nella stazione di ricarica '{{chargeBoxID}}', presa '{{connectorId}}' è stata avviata con successo nell'organizzazione '{{tenantName}}'"}, "ocpiPatchChargingStationsStatusesError": {"title": "Stati d'Invio in Roaming", "body": "Il trasferimento alla piattaforma di roaming degli stati della stazione di ricarica è fallito nella posizione '{{location}}' nell'organizzazione '{{tenantName}}', controlla i log"}, "oicpPatchChargingStationsStatusesError": {"title": "Hubject - Stato delle stazioni di ricarica", "body": "Il trasferimento alla piattaforma Hubject dello stato delle stazioni di ricarica é fallito nell'organizzazione '{{tenantName}}', controlla i log"}, "oicpPatchChargingStationsError": {"title": "Hubject - <PERSON><PERSON> errori stazioni di ricarica", "body": "Il trasferimento alla piattaforma Hubject dei dati delle stazioni di ricarica é fallito nell'organizzazione '{{tenantName}}', controlla i log"}, "optimalChargeReached": {"title": "Carica Ottimale Raggiunta", "body": "Il tuo veicolo elettrico, connesso a '{{chargeBoxID}}', presa '{{connectorId}}', ha raggiunto la sua carica ottimale (85%) nell'organizzazione '{{tenantName}}'"}, "endOfCharge": {"title": "Carica Terminata", "body": "Il tuo veicolo elettrico, connesso alla stazione di ricarica '{{chargeBoxID}}', presa '{{connectorId}}', ha appena finito di caricare nell'organizzazione '{{tenantName}}'"}, "endOfSession": {"title": "Sessione Terminata", "body": "La tua sessione sulla stazione di ricarica '{{chargeBoxID}}', presa '{{connectorId}}' è terminata nell'organizzazione '{{tenantName}}'"}, "chargingStationStatusError": {"title": "Stazione di Ricarica in Errore", "body": "Si è verificato un errore in '{{chargeBoxID}}', presa '{{connectorId}}' dell'organizzazione '{{tenantName}}': {{error}}"}, "unknownUserBadged": {"title": "Utente Sconosciuto", "body": "Un utente sconosciuto si è registrato su '{{chargeBoxID}}' con il badge RFID '{{badgeID}}' nell'organizzazione '{{tenantName}}'"}, "chargingStationRegistered": {"title": "Stazione di Ricarica Connessa", "body": "La stazione di ricarica '{{chargeBoxID}}' si è appena connessa al server centrale nell'organizzazione '{{tenantName}}'"}, "userAccountStatusChanged": {"title": "Account {{status}}", "activated": "attivat<PERSON>", "suspended": "sospeso", "body": "Il tuo account è stato {{status}} da un amministratore nell'organizzazione '{{tenantName}}'"}, "userWalletMinimumAmtReached": {"title": "Wallet Balance Reached Minimum Amount", "body": "You Wallet balance has been reached minimum Amout. Please recharge your wallet and continue charging. "}, "userAccountInactivity": {"title": "Account Utente", "body": "Il tuo account è inattivo da {{lastLogin}} nell'organizzazione '{{tenantName}}'. Per motivi di conformità, tieni presente che gli account inattivi vecchi di 6 mesi verranno eliminati"}, "preparingSessionNotStarted": {"title": "Sessione Non Avviata", "body": "Sessione non avviata nella stazione di ricarica '{{chargeBoxID}}', presa '{{connectorId}}' nell'organizzazione '{{tenantName}}'"}, "offlineChargingStation": {"title": "Stazioni di Ricarica Offline", "body": "Le seguenti stazioni di ricarica sono offline nell'organizzazione '{{tenantName}}': {{chargeBoxIDs}}"}, "billingUserSynchronizationFailed": {"title": "Sincronizzazione Utente Fallita", "body": "Impossibile sincronizzare {{nbrUsersInError}} utenti con il fornitore dei servizi di fatturazione nell'organizzazione '{{tenantName}}'"}, "billingInvoiceSynchronizationFailed": {"title": "Sincronizzazione Fattura Fallita", "body": "Impossibile sincronizzare {{nbrInvoicesInError}} fatture con il fornitore dei servizi di fatturazione nell'organizzazione '{{tenantName}}'"}, "billingPeriodicOperationFailed": {"title": "Fatturazione periodica", "body": "Non é stato possibile trattare {{nbrInvoicesInError}} fattura(e). Organizzazione '{{tenantName}}'"}, "computeAndApplyChargingProfilesFailed": {"title": "Profili di ricarica falliti", "body": "Impossibile impostare i profili di ricarica per '{{chargeBoxID}}' sull'area del sito '{{siteAreaName}}' nell'organizzazione '{{tenantName}}'"}, "billingNewInvoicePaid": {"title": "Nuova fattura pagata", "body": "Una nuova fattura ({{invoiceNumber}}) dell'ammontare di {{amount}} é stata pagata ed é ora disponibile"}, "billingNewInvoiceOpen": {"title": "Nuova fattura da pagare", "body": "Una nuova fattura ({{invoiceNumber}}) dell'ammontare di {{amount}} é ora disponibile. Si prega di controllare la propria email per finalizzare il pagamento."}, "billingAccountCreationLink": {"title": "Creazione dell'account per la fatturazione", "body": "L'invito per la creazione di un account per la fatturazione é stato mandato. Si prega di controllare la propria email per finalizzare la creazione."}, "billingAccountActivated": {"title": "Account per la fatturazione attivato", "body": "L'account per la fatturazione é stato attivato"}, "endUserErrorNotification": {"title": "Un utente ha riportato un errore", "body": "'{{userName}}' ha riportato: '{{errorTitle}}' - '{{errorDescription}}' nell'organizzazione '{{tenantName}}'"}, "accountVerificationNotification": {"title": "Account verificato", "bodyVerified": "Il tuo account é stato verificato con successo. Un amministratore controllerà e validerà il tuo account", "bodyVerifiedAndActivated": "Il tuo account é stato verificato e validato con successo"}}, "siteArea": "Area del Sito", "statistics": {"consumption": "Consumo (kW.h)", "inactivity": "Periodo di inattività (Ore)", "numberOfSessions": "Numero di Sessioni", "usage": "<PERSON><PERSON> (Ore)"}, "tags": {"id": "Identificazione del Badge", "description": "Descrizione del Badge", "virtualBadge": "Badge Virtuale"}, "transactions": {"totalConsumption": "Consumo Totale (kW.h)", "totalDuration": "Durata totale (Minuti)", "totalInactivity": "Periodo di inattività totale (Minuti)"}, "users": {"email": "Email", "eulaAcceptedOn": "Contratto di Licenza accettato il ", "firstName": "Nome", "id": "ID", "role": "<PERSON><PERSON><PERSON>", "status": "Stato", "user": "Utilizzatore", "userID": "ID Utilizzatore"}, "email": {"greetings": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{recipientName}}", "footerText": "Un messaggio automatico é stato inviato a {{recipientEmail}}. Si prega di non rispondere. Puoi annullare l'iscrizione modificando le impostazioni di notifica.", "new-registered-user": {"title": "Account creato", "buttonText": "Attivare l'account", "text": ["L'account é stato creato con successo", "", "Cliccare sur link sottostante per attivarlo"]}, "request-password": {"title": "Reimpostare la Password", "buttonText": "Reimpostare la Password", "text": ["E' stata richiesta la reinizializzazione della password", "", "Cliccare sur link sottostante per inserire la nuova password"]}, "optimal-charge-reached": {"title": "Carica ottimale raggiunta", "buttonText": "Visualizzare la sessione", "text": ["Il veicolo, connesso a {{chargeBoxID}}, presa {{connectorId}}, ha raggiunto la sua carica ottimale."], "table": [{"label": "Livello di carica della batteria", "value": "{{stateOfCharge}} %"}]}, "end-of-session": {"title": "Sessione finita", "buttonText": "Visualizzare la sessione", "text": ["La sessione sulla stazione di ricarica {{chargeBoxID}}, presa {{connectorId}} é appena terminata."], "table": [{"label": "Consu<PERSON>", "value": "{{totalConsumption}} kW.h"}, {"label": "Durata totale", "value": "{{totalDuration}}"}, {"label": "Inattività totale", "value": "{{totalInactivity}}"}, {"label": "Livello di carica", "value": "{{stateOfCharge}} %"}]}, "end-of-signed-session": {"title": "Session Finished - Signed Session", "buttonText": "View Session", "text": ["Ladetransaktion abgeschlossen"], "table": [{"label": "Charger", "value": "{{chargeBoxID}}"}, {"label": "Connector", "value": "{{connectorId}}"}, {"label": "RFID Card", "value": "{{tagId}}"}, {"label": "Start time", "value": "{{startDate}}"}, {"label": "End time", "value": "{{endDate}}"}, {"label": "Start counter", "value": "{{meterStart}} kWh"}, {"label": "Stop counter", "value": "{{meterStop}} kWh"}, {"label": "Consumption", "value": "{{totalConsumption}} kWh"}, {"label": "Cost", "value": "{{price}}€ {{relativeCost}}€/kWh) (informative)"}, {"label": "Contract for", "value": "LOCAL (informative)"}]}, "end-of-charge": {"title": "Carica terminata", "buttonText": "Visualizzare la sessione", "text": ["Il veicolo, connesso alla stazione di ricarica {{chargeBoxID}}, presa {{connectorId}}, ha appena finito la ricarica."], "table": [{"label": "Consu<PERSON>", "value": "{{totalConsumption}} kW.h"}, {"label": "Durata totale", "value": "{{totalDuration}}"}, {"label": "Livello di carica della batteria", "value": "{{stateOfCharge}} %"}]}, "billing-new-invoice-paid": {"title": "Fatturazione - Ricevuta pagata", "buttonText": "Scaricare fattura", "text": ["La fattura {{invoiceNumber}} é stata pagata e il documento corrispondente può essere scaricato."], "table": [{"label": "Prezzo pagato", "value": "{{invoiceAmount}}"}]}, "billing-new-invoice-unpaid": {"title": "Fatturazione - Nuova Ricevuta", "buttonText": "Pa<PERSON>e la fattura", "text": ["La fattura {{invoiceNumber}} é stata generata e il documento corrispondente può essere scaricato.", "", "Cliccare sur link sottostante per finalizzare il pagamento"], "table": [{"label": "Importo dovuto", "value": "{{invoiceAmount}}"}]}, "charging-station-registered": {"title": "Stazione di Ricarica Connessa", "buttonText": "Visualizzare la stazione di ricarica", "text": ["La stazione di ricarica {{chargeBoxID}} é stata connessa al server centrale."]}, "end-user-error-notification": {"title": "Un utente ha riportato un errore", "buttonText": "Navigare verso {{tenantName}}", "text": ["L'utente {{name}} ha riportato l'errore seguente:", "", "Errore: {{errorTitle}}", "Descrzione: {{errorDescription}}", "", "Email: {{email}}", "Telefono: {{phone}}"]}, "offline-charging-station": {"title": "Stazioni di Ricarica Offline", "buttonText": "Navigare verso {{tenantName}}", "text": ["La tua organizzazione ha {{nbChargingStationIDs}} stazione(i) di ricarica offline.", "", "Stazioni di Ricarica: {{tenFirstChargingStationIDs}}"]}, "charging-station-status-error": {"title": "Errore su {{chargeBoxID}}, Presa {{connectorId}}", "buttonText": "Visualizzare l'errore", "text": ["Si é verificato un errore su {{chargeBoxID}}, presa {{connectorId}}", "Error: {{error}}"]}, "billing-account-created": {"title": "Account di fatturazione creato", "buttonText": "Continua", "text": ["Un account di fatturazione è stato creato per tuo account da un amministratore.", "", "Cliccare sur link sottostante per finalizzare l'attivazione dell'account"]}, "user-account-status-changed": {"title": "Stato dell'account utente modificato", "buttonText": "Accedere a {{tenantName}}", "text": ["Lo stato del tuo account è stato modificato da un amministratore", "Nuovo stato: {{accountStatus}}"]}, "unknown-user-badged": {"title": "Un utente sconosciuto ha tentato di usare il badge", "buttonText": "Accedere a {{tenantName}}", "text": ["Un utente sconosciuto ha usato il badge su {{chargeBoxID}}, numero di badge {{badgeID}}."]}, "session-started": {"title": "Sessione iniziata", "buttonText": "Visualizzare la sessione", "text": ["Il veicolo é stato connesso alla stazione di ricarica {{chargeBoxID}} sulla presa {{connectorId}}."]}, "verification-email": {"title": "Attivazione dell'account", "buttonText": "Attivare l'account", "text": ["Una richiesta é stata iniziata per attivare un account", "", "Cliccare sur link sottostante per completare l'attivazione"]}, "verification-email-user-import": {"title": "Account creato", "buttonText": "Attivare l'account", "text": ["Un account é stato creato", "", "Cliccare sur link sottostante per completare l'attivazione"]}, "ocpi-patch-status-error": {"title": "Impossibile trasmettere lo stato delle stazioni di ricarica", "buttonText": "Accedere a {{tenantName}}", "text": ["Il trasferimento dello stato delle stazioni di ricarica del sito {{location}} verso la piattaforma di roaming OCPI é fallito.", "", "Controllare i logs."]}, "oicp-patch-status-error": {"title": "Hubject - invio degli stati", "buttonText": "Accedere a {{tenantName}}", "text": ["La trasmissione dello stato delle stazioni di ricarica verso la piattaforma roaming Hubject é fallito nell'organizzazione '{{tenantName}}'.", "", "Controllare i logs."]}, "oicp-patch-evses-error": {"title": "Hubject - invio stazioni di ricarica", "buttonText": "Accedere a {{tenantName}}", "text": ["La trasmissione della lista delle stazioni di ricarica verso la piattaforma roaming Hubject é fallito nell'organizzazione '{{tenantName}}'.", "", "Controllare i logs."]}, "user-account-inactivity": {"title": "Inattività account utente", "buttonText": "Accedere a {{tenantName}}", "text": ["Non hai effettuato l'accesso all'applicatione E-Mobility dal {{lastLogin}}.", "Si tenga presente che, per ragioni legali sulla protezione dei dati personali, l'account potrebbe essere eliminato da un amministratore.", "", "Si prega di effettuare il login e accettare l'ultimo contratto di licenza per evitare l'eliminazione dell'account."]}, "session-not-started": {"title": "Nessuna sessione iniziata", "buttonText": "Visualizzare la stazione di ricarica", "text": ["La sessione sulla stazione di ricarica {{chargeBoxID}}, presa {{connectorId}} non è stata avviata."]}, "session-not-started-after-authorize": {"title": "Nessuna sessione iniziata", "buttonText": "Visualizzare la stazione di ricarica", "text": ["Un badge è stato scannerizzato sulla stazione di ricarica {{chargeBoxID}}, ma nessuna session è stata avviata."]}, "billing-user-synchronization-failed": {"title": "Fatturazione - Sincronizzazione utente fallita", "buttonText": "Accedere a {{tenantName}}", "text": ["Impossibile sincronizzare {{nbrUsersInError}} utente(i) con il servizio di fatturazione."]}, "billing-invoice-synchronization-failed": {"title": "Fatturazione - Sincronizzazione fatture fallita", "buttonText": "Accedere a {{tenantName}}", "text": ["Impossibile sincronizzare {{nbrInvoicesInError}} fattura(e) con il servizio di fatturazione."]}, "billing-periodic-operation-failed": {"title": "Fatturazione - Procedura di fatturazione periodica fallita", "buttonText": "Accedere a {{tenantName}}", "text": ["Impossibile trattare {{nbrInvoicesInError}} fattura(e)."]}, "billing-account-activated": {"title": "Account di Fatturazione attivato", "buttonText": "Accedere a {{tenantName}}", "text": ["Congratulazioni!", "", "La registrazione è stata completata con successo e l'account di fatturazione è stato attivato."]}, "car-synchronization-failed": {"title": "Sincronizzazione dati del veicolo fallita", "buttonText": "Accedere a {{tenantName}}", "text": ["Impossibile sincronizzare i dati per {{nbrCarsInError}} veicolo(i)."]}, "compute-and-apply-charging-profiles-failed": {"title": "Procedura di applicazione dei profili di carica fallita", "buttonText": "Accedere a {{tenantName}}", "text": ["L'applicazione del profilo di ricarica alla stazione di ricarica {{chargeBoxID}} è fallita.", "La stazione di ricarica è stata esclusa dall'attuale sessione di carica (Ricarica Smart)."]}, "account-verification-notification-active": {"title": "Account verificato", "buttonText": "Accedere a {{tenantName}}", "text": ["L'account é stato creato e attivato con successo!"]}, "account-verification-notification-inactive": {"title": "Account verificato", "buttonText": "Accedere a {{tenantName}}", "text": ["L'email è stata verificata con successso.", "", "Un amministratore procederà alla validazione e all'attivazione dell'account."]}, "admin-account-verification-notification": {"title": "Verifica dell'account", "buttonText": "Verifica account", "text": ["Un nuovo utente ha appena creato un account con email {{email}}", "", "Cliccare sur link sottostante per attivate l'account."]}, "user-create-password": {"title": "Account creato", "buttonText": "Impostare una nuova password", "text": ["Un account é stato creato nell'organizzazione '{{tenantName}}'.", "Si prega di effettuare il login almeno una volta per attivate la possibilità di ricaricare il proprio veicolo.", "", "Cliccare sur link sottostante per impostare la password."]}}}