import BackendError from '../../exception/BackendError';
import Constants from "../../utils/Constants";
import { DataResult } from "../../types/DataResult";
import DatabaseUtils from "./DatabaseUtils";
import DbParams from "../../types/database/DbParams";
import Logging from '../../utils/Logging';
import { ObjectId } from 'mongodb'
import Tenant from "../../types/Tenant";
import Utils from "../../utils/Utils";
import Pages from '../../types/Pages';
import global, { DatabaseCount, FilterParams} from '../../types/GlobalType';// added to remove comming below

const MODULE_NAME = 'PagesStorage';

export default class PagesStorage {

  public static async savePages(tenant: Tenant, PagesToSave: Pages, saveImage = false): Promise<any> {
    const startTime = Logging.traceDatabaseRequestStart();
    DatabaseUtils.checkTenantObject(tenant);
    // Check if ID or slug
    if (!PagesToSave.id && !PagesToSave.slug) {
      throw new BackendError({
        module: MODULE_NAME,
        method: 'savePages',
        message: 'Pages has no ID and no slug'
      });
    }
    // Build Request
    const pagesFilter: any = {};
    if (PagesToSave.id) {
      pagesFilter._id = DatabaseUtils.convertToObjectID(PagesToSave.id);
    } else {
      pagesFilter.slug = PagesToSave.slug;
    }
    const PagesMDB: any = {
      _id: PagesToSave.id ? DatabaseUtils.convertToObjectID(PagesToSave.id) : new ObjectId(),
      slug: PagesToSave.slug,
      description: PagesToSave.description,
      status: PagesToSave.status,
      title: PagesToSave.title,
    }
    // Check if isDeleted field is true/false
    if (PagesToSave.isDeleted) {
      PagesMDB.isDeleted = PagesToSave.isDeleted;
    } else {
      PagesMDB.isDeleted = false;
    }
    //  Check Created/Last Changed By
    DatabaseUtils.addLastChangedCreatedProps(PagesMDB, PagesToSave);
    // Modify and return the modified document
    let ab = await global.database.getCollection<any>(tenant.id, 'pages')
      .findOneAndUpdate(
        pagesFilter,
        { $set: PagesMDB },
        { upsert: true, returnDocument: 'after' });
    await Logging.traceDatabaseRequestEnd(tenant, MODULE_NAME, 'savePages', startTime, PagesMDB);
    return PagesMDB._id;

  }
  public static async insertManyPages(tenant: Tenant): Promise<any[]> {
    const startTime = Logging.traceDatabaseRequestStart();
    DatabaseUtils.checkTenantObject(tenant);
    const pagesToInsert = Constants.STATIC_PAGES.map(page => {
      const pageMDB: any = {
        _id: new ObjectId(),
        slug: page.slug,
        description: page.description,
        status: 'active',
        title: page.title,
        isDeleted: false,
      };
      DatabaseUtils.addLastChangedCreatedProps(pageMDB, page);
      return pageMDB;
    });
    const result = await global.database.getCollection<any>(tenant.id, 'pages').insertMany(pagesToInsert);
    await Logging.traceDatabaseRequestEnd(tenant, MODULE_NAME, 'insertManyPages', startTime, pagesToInsert);
    return Object.values(result.insertedIds);

  }
  public static async getPageBySlug(tenant: Tenant, slug: string = Constants.UNKNOWN_STRING_ID): Promise<Pages> {
    const pagesMDB = await PagesStorage.getPages(tenant, {
     slug : [slug],
    }, Constants.DB_PARAMS_SINGLE_RECORD);
    return pagesMDB.count === 1 ? pagesMDB.result[0] : null;
 }

  public static async getpage(tenant: Tenant, id: string = Constants.UNKNOWN_OBJECT_ID,
    params: { title?: string; slug?: string; } = {},
    projectFields?: string[]): Promise<Pages> {
    const PagesMDB = await PagesStorage.getPages(tenant, {
      pagesid: [id],
    }, Constants.DB_PARAMS_SINGLE_RECORD, projectFields);
    return PagesMDB.count === 1 ? PagesMDB.result[0] : null;
  }

  public static async getPages(tenant: Tenant,
    params: {
      title?: string; search?: string; slug?: string[];
      status?: string; pagesid?: string[]; description?: string, createdDate?: Date; updatedDate?: Date
    },
    dbParams: DbParams, projectFields?: string[]): Promise<DataResult<Pages>> {
 
    const startTime = Logging.traceDatabaseRequestStart();
    DatabaseUtils.checkTenantObject(tenant);
    // Clone before updating the values
    dbParams = Utils.cloneObject(dbParams);
    // Check Limit
    dbParams.limit = Utils.checkRecordLimit(dbParams.limit);
    // Check Skip
    dbParams.skip = Utils.checkRecordSkip(dbParams.skip);
    const filters: FilterParams = {};
    // Create Aggregation
    const aggregation = [];
    // Filter
    if (params.search) {
      filters.$or = [
        { 'slug': { $regex: params.search, $options: 'i' } },
        { 'description': { $regex: params.search, $options: 'i' } },
        { 'title': { $regex: params.search, $options: 'i' } }
      ];
      if (DatabaseUtils.isObjectID(params.search)) {
        filters.$or.push({ '_id': DatabaseUtils.convertToObjectID(params.search) });
      }
    }
    // Users
    if (!Utils.isEmptyArray(params.pagesid)) {
      filters._id = { $in: params.pagesid.map((page) => DatabaseUtils.convertToObjectID(page)) };
    }
    // title
    if (params.title) {
      filters.title = params.title;
    }

    // Users
    if (!Utils.isEmptyArray(params.slug)) {
      filters.slug = { $in: params.slug.map((slug) => slug) };
    }

    aggregation.push({
      $match: filters
    });

    // Limit records?
    if (!dbParams.onlyRecordCount) {
      // Always limit the nbr of record to avoid perfs issues
      aggregation.push({ $limit: Constants.DB_RECORD_COUNT_CEIL });
    }
    // Count Records
    const usersCountMDB = await global.database.getCollection<any>(tenant.id, 'pages')
      .aggregate([...aggregation, { $count: 'count' }], DatabaseUtils.buildAggregateOptions())
      .toArray() as DatabaseCount[];
    // Check if only the total count is requested
    if (dbParams.onlyRecordCount) {
      // Return only the count
      await Logging.traceDatabaseRequestEnd(tenant, MODULE_NAME, 'pages', startTime, usersCountMDB);
      return {
        count: (!Utils.isEmptyArray(usersCountMDB) ? usersCountMDB[0].count : 0),
        result: []
      };
    }
    // Remove the limit
    aggregation.pop();
    // Sort
    if (!dbParams.sort) {
      dbParams.sort = { status: -1};
    }
    aggregation.push({
      $sort: dbParams.sort
    });
    // Skip
    aggregation.push({
      $skip: dbParams.skip
    });
    // Limit
    aggregation.push({
      $limit: dbParams.limit
    });
    // Change ID
    DatabaseUtils.pushRenameDatabaseID(aggregation);
    // Add Created By / Last Changed By
    DatabaseUtils.pushCreatedLastChangedInAggregation(tenant.id, aggregation);
    // Project

    DatabaseUtils.projectFields(aggregation, projectFields);
    // Read DB
    const usersMDB = await global.database.getCollection<any>(tenant.id, 'pages')
      .aggregate<any>(aggregation, DatabaseUtils.buildAggregateOptions())
      .toArray() as Pages[];
    await Logging.traceDatabaseRequestEnd(tenant, MODULE_NAME, 'pages', startTime, usersMDB);
    return {
      count: DatabaseUtils.getCountFromDatabaseCount(usersCountMDB[0]),
      result: usersMDB,
      projectFields: projectFields
    };
  }

}
