{"billing": {"generatedUser": "Generovat uživatele pro '{{email}}'", "chargingAtSiteArea": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> relace: {{sessionID}} - po<PERSON><PERSON><PERSON>ční datum: {{startDate}} v {startTime} - spotřeba energie: {{totalConsumption}} kWh v {{siteAreaName}}", "chargingAtChargeBox": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> relace: {{sessionID}} - po<PERSON><PERSON><PERSON>č<PERSON><PERSON> datum: {{startDate}} v {{startTime}} - spotřeba energie: {{totalConsumption}} kWh nabíje<PERSON><PERSON> stanice {{chargeBoxID}}", "header-itemDescription": "Session: {{sessionID}} - Start Date: {{startDate}} at {{startTime}} at charging station {{chargeBoxID}}", "flatFee-shortItemDescription": "Flat Fee", "energy-shortItemDescription": "Energy Consumption: {{quantity}} kWh", "chargingTime-shortItemDescription": "Charging Time: {{duration}}", "parkingTime-shortItemDescription": "Parking Time: {{duration}}", "transfer-feeItemDescription": "Fee charged for {{nbSessions}} session(s)"}, "chargers": {"chargeBoxSN": "Nabíjecí stojan S/N", "chargePointSN": "Nabíjecí místo S/N", "chargingStation": "Nabíjecí stanice", "connector": "Konektor", "firmwareVersion": "Verze firmwaru", "lastReboot": "Poslední restart", "lastSeen": "Naposledy zobrazeno", "maxPower": "Maximální výkon (W)", "model": "Model", "numberOfConnectors": "Počet konektorů", "ocppProtocol": "Protokol OCPP", "ocppVersion": "Verze OCPP", "powerLimitUnit": "<PERSON><PERSON> v<PERSON><PERSON>", "timezone": "Časové pásmo nabíjecích stanic", "vendor": "Prodejce"}, "general": {"changedBy": "Změněno uživatelem", "changedOn": "Změněno dne", "createdOn": "Vytvořeno dne", "date": "Datum", "endDate": "<PERSON><PERSON>", "endTime": "<PERSON>as <PERSON>", "invalidDate": "Neplat<PERSON><PERSON> datum", "invalidTime": "Neplatný čas", "latitude": "Zeměpisná šířka", "longitude": "Zeměpisná délka", "month": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Jméno", "price": "<PERSON><PERSON>", "priceUnit": "<PERSON><PERSON><PERSON>", "site": "Parkoviště", "siteArea": "<PERSON><PERSON><PERSON>", "startDate": "Počáteční datum", "startTime": "Počáteční čas", "time": "Čas", "value": "Hodnota", "year": "Rok"}, "loggings": {"action": "Ak<PERSON>", "host": "Hostitel", "level": "Úroveň", "message": "Zpráva", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "module": "<PERSON><PERSON><PERSON>", "process": "Proces", "source": "<PERSON><PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON>"}, "notifications": {"sessionNotStarted": {"title": "<PERSON><PERSON>", "body": "Prokázali jste se kartou/čipem na nabíjecí stanici '{{chargeBoxID}}', ale relace nebyla z<PERSON>na"}, "sessionStarted": {"title": "<PERSON><PERSON>", "body": "<PERSON><PERSON><PERSON><PERSON> relace na nabíjecí stanici '{{chargeBoxID}}', konektor '{{connectorId}}', by<PERSON><PERSON>šně zahájena u organizace '{{tenantName}}'"}, "ocpiPatchChargingStationsStatusesError": {"title": "Odeslat stavy pomocí roamingu", "body": "Př<PERSON>d stavů nabíjecí stanice na roamingovou platformu se u lokality '{{location}}‘ organizace '{{tenantName}}‘ nezdařil, zkontrolujte protokol"}, "oicpPatchChargingStationsStatusesError": {"title": "Odeslat stavy platformě <PERSON>", "body": "Př<PERSON>d stavů nabíjecí stanice  na roamingovou platformu Hubject se u organizace '{{tenantName}}' nezda<PERSON>il, zkontrolujte protokol"}, "oicpPatchChargingStationsError": {"title": "Odeslat nabíjecí stanice platformě Hubject", "body": "Př<PERSON>d stavů nabíjecí stanice na roamingovou platformu Hubject se u organizace '{{tenantName}}' nezda<PERSON>il, zkontrolujte protokol"}, "optimalChargeReached": {"title": "Dosaženo optimálního nabití", "body": "<PERSON>a<PERSON><PERSON> elektric<PERSON> v<PERSON>, které je přip<PERSON>no k nabíjecí stanici '{{chargeBoxID}}', konektor '{{connectorId}}', dosáhlo u organizace '{{tenantName}}' optimálního nabití (85 %) "}, "endOfCharge": {"title": "Nabíje<PERSON><PERSON>", "body": "Va<PERSON>e elektric<PERSON> v<PERSON>, které je připo<PERSON>no k nabíjecí stanici '{{chargeBoxID}}', konektor '{{connectorId}}', právě dokončilo nabíjení u organizace '{{tenantName}}'"}, "endOfSession": {"title": "<PERSON><PERSON>", "body": "<PERSON><PERSON><PERSON><PERSON> relace na nabíjecí stanici '{{chargeBoxID}}', konektor '{{connectorId}}', právě skončila u organizace '{{tenantName}}'"}, "chargingStationStatusError": {"title": "Vadná nabíjecí stanice", "body": "<PERSON><PERSON><PERSON> k <PERSON>ě na nabíjecí stanici '{{chargeBoxID}}', konektor '{{connectorId}}', organizace '{{tenantName}}': {{error}"}, "unknownUserBadged": {"title": "Neznámý už<PERSON>l", "body": "Neznámý uživatel se prokázal kartou/čipem s ID '{{badgeID}}' na nabíjecí stanici '{{chargeBoxID}}' organizace {{tenantName}}"}, "chargingStationRegistered": {"title": "Nabíjecí stanice př<PERSON>na", "body": "Nabíje<PERSON>í stanice '{{chargeBoxID}}' se právě připojila k centrálnímu serveru organizace '{{tenantName}}"}, "userAccountStatusChanged": {"title": "Účet {{status}}", "activated": "aktivován", "suspended": "pozast<PERSON>", "body": "<PERSON><PERSON><PERSON> byl {status}} správcem organizace '{{tenantName}}'"}, "userWalletMinimumAmtReached": {"title": "Wallet Balance Reached Minimum Amount", "body": "You Wallet balance has been reached minimum Amout. Please recharge your wallet and continue charging. "}, "userAccountInactivity": {"title": "Uživatelský účet", "body": "<PERSON><PERSON><PERSON>et u organizace '{{tenantName}}' byl neaktivní od {{lastLogin}}. Uvědomte  si prosím, že z důvodů dodržování předpisů budou ú<PERSON>ty neaktivní po dobu 6 měs<PERSON><PERSON><PERSON> vymazány"}, "preparingSessionNotStarted": {"title": "<PERSON><PERSON>", "body": "Relace ne<PERSON> na nabíjecí stanici '{{chargeBoxID}}', konektor '{{connectorId}}', organizace '{{tenantName}}'"}, "offlineChargingStation": {"title": "Nabíjecí stanice offline", "body": "Následující nabíjecí stanice organizace {{tenantName}}‘ jsou offline: {{chargeBoxIDs}"}, "billingUserSynchronizationFailed": {"title": "Synchronizace uživatele se nezdařila", "body": "Nelze synchronizovat počet {{nbrUsersInError}} uživatelů s poskytovatelem fakturační služby v organizaci '{{tenantName}}'"}, "billingInvoiceSynchronizationFailed": {"title": "Synchronizace faktury se nezdařila", "body": "Nelze synchronizovat počet {{nbrUsersInError}} faktur s poskytovatelem fakturační služby v organizaci '{{tenantName}}'"}, "billingPeriodicOperationFailed": {"title": "<PERSON><PERSON><PERSON> f<PERSON>ční operace", "body": "Zpracování počtu {{nbrInvoicesInError}} faktur se nezdařilo. Organizace '{{tenantName}}'"}, "computeAndApplyChargingProfilesFailed": {"title": "Nabíje<PERSON><PERSON> profily se<PERSON>y", "body": "Nelze nastavit nabíjecí profily na nabíjecí stanici '{{chargeBoxID}}‘ v sekci parkoviště '{{siteAreaName}}' organizace '{{tenantName}}'"}, "billingNewInvoicePaid": {"title": "Nová faktura zaplacena", "body": "<PERSON><PERSON><PERSON><PERSON> fak<PERSON> (č. {{invoiceNumber}}) ve výši {{amount}} € byla zaplacena a je nyní k dispozici"}, "billingNewInvoiceOpen": {"title": "Nová faktura k zaplacení", "body": "Nová faktura (č. {{invoiceNumber}}) ve výši {{amount}} € je nyní k dispozici. Zkontrolujte prosím svůj e-mail kvůli dokončení platby"}, "billingAccountCreationLink": {"title": "Billing account onboarding", "body": "You have been invited to create a billing account. Please check your email to complete the process"}, "billingAccountActivated": {"title": "Billing account activated", "body": "Your billing account has been activated"}, "endUserErrorNotification": {"title": "Uživatel oznámil chybu", "body": "Uživatel '{{userName}}' oznámil: '{{errorTitle}}' - '{{errorDescription}}' v organizaci '{{tenantName}}'"}, "accountVerificationNotification": {"title": "Účet ověřen", "bodyVerified": "<PERSON><PERSON>š <PERSON>et byl úspěšně ověřen. Správce váš účet zkontroluje a bude ho aktivovat.", "bodyVerifiedAndActivated": "<PERSON><PERSON><PERSON> byl úspěšně ověřen a aktivován."}}, "siteArea": "<PERSON><PERSON><PERSON>", "statistics": {"consumption": "Spotřeba (kWh)", "inactivity": "Nečinnost (hodiny)", "numberOfSessions": "Počet relací", "usage": "V<PERSON><PERSON>ití (hodiny)"}, "tags": {"id": "ID karty/čipu", "description": "Popis karty/čipu", "virtualBadge": "Virtuální karta/čip"}, "transactions": {"totalConsumption": "<PERSON><PERSON><PERSON> (kWh)", "totalDuration": "<PERSON><PERSON><PERSON> doba tr<PERSON> (min.)", "totalInactivity": "<PERSON><PERSON><PERSON> (min.)"}, "users": {"email": "E-mail", "eulaAcceptedOn": "EULA přijata dne", "firstName": "První j<PERSON>", "id": "ID", "role": "Role", "status": "Stav", "user": "<PERSON>živatel", "userID": "ID uživatele"}, "email": {"greetings": "Hello {{recipientName}}", "footerText": "This message has been sent by an automated email service to {{recipientEmail}}. Please do not reply. You may unsubscribe by changing your notification settings.", "new-registered-user": {"title": "User Account Created", "buttonText": "Activate your account", "text": ["Your account has been created successfully.", "", "Click on the link below to activate it."]}, "request-password": {"title": "Reset Password", "buttonText": "Reset Password", "text": ["You have just requested to reset your password.", "", "Click on the link below to set the new one."]}, "optimal-charge-reached": {"title": "Optimal Charge Reached", "buttonText": "View Session", "text": ["Your vehicle, which is connected to {{chargeBoxID}}, connector {{connectorId}}, reached its optimal charge."], "table": [{"label": "Session", "value": "{{transactionId}}"}, {"label": "Battery Level", "value": "{{stateOfCharge}} %"}]}, "end-of-session": {"title": "Session Finished", "buttonText": "View Session", "text": ["Your session on the charging station {{chargeBoxID}}, connector {{connectorId}} has just finished."], "table": [{"label": "Session", "value": "{{transactionId}}"}, {"label": "Consumption", "value": "{{totalConsumption}} kW.h"}, {"label": "Total Duration", "value": "{{totalDuration}}"}, {"label": "Total Inactivity", "value": "{{totalInactivity}}"}, {"label": "State of Charge", "value": "{{stateOfCharge}} %"}]}, "end-of-signed-session": {"title": "Session Finished - Signed Session", "buttonText": "View Session", "text": ["Ladetransaktion abgeschlossen"], "table": [{"label": "Charger", "value": "{{chargeBoxID}}"}, {"label": "Connector", "value": "{{connectorId}}"}, {"label": "RFID Card", "value": "{{tagId}}"}, {"label": "Start time", "value": "{{startDate}}"}, {"label": "End time", "value": "{{endDate}}"}, {"label": "Start counter", "value": "{{meterStart}} kWh"}, {"label": "Stop counter", "value": "{{meterStop}} kWh"}, {"label": "Consumption", "value": "{{totalConsumption}} kWh"}, {"label": "Cost", "value": "{{price}}€ {{relativeCost}}€/kWh) (informative)"}, {"label": "Contract for", "value": "LOCAL (informative)"}]}, "end-of-charge": {"title": "Charge Finished", "buttonText": "View Session", "text": ["Your vehicle, which is connected to the charging station {{chargeBoxID}}, connector {{connectorId}}, has just finished charging."], "table": [{"label": "Session", "value": "{{transactionId}}"}, {"label": "Consumption", "value": "{{totalConsumption}} kW.h"}, {"label": "Total Duration", "value": "{{totalDuration}}"}, {"label": "Battery Level", "value": "{{stateOfCharge}} %"}]}, "billing-new-invoice-paid": {"title": "Billing - Paid Invoice", "buttonText": "Download Invoice", "text": ["Your invoice {{invoiceNumber}} has been paid and the corresponding document can now be downloaded."], "table": [{"label": "Amount <PERSON>", "value": "{{invoiceAmount}}"}]}, "billing-new-invoice-unpaid": {"title": "Billing - New Invoice", "buttonText": "Pay Invoice", "text": ["The invoice {{invoiceNumber}} has been generated and the corresponding document can now be downloaded.", "", "Please follow the link below to finalize the payment"], "table": [{"label": "Amount Due", "value": "{{invoiceAmount}}"}]}, "charging-station-registered": {"title": "Charging Station Connected", "buttonText": "View Charging Station", "text": ["The charging station {{chargeBoxID}} just got connected to the central server."]}, "end-user-error-notification": {"title": "A User Reported an Error", "buttonText": "Navigate to {{tenantName}}", "text": ["The user {{name}} reported the following error:", "", "Error: {{errorTitle}}", "Description: {{errorDescription}}", "", "Email: {{email}}", "Phone: {{phone}}"]}, "offline-charging-station": {"title": "Offline Charging Stations", "buttonText": "Navigate to {{tenantName}}", "text": ["Your organization has {{nbChargingStationIDs}} charging station(s) being offline.", "", "Charging Stations: {{tenFirstChargingStationIDs}}"]}, "charging-station-status-error": {"title": "Error on {{chargeBoxID}}, connector {{connectorId}}", "buttonText": "View Error", "text": ["An error occurred on {{chargeBoxID}}, connector {{connectorId}}", "Error: {{error}}"]}, "billing-account-created": {"title": "Billing Account Onboarding", "buttonText": "Proceed", "text": ["A billing account has been created on your behalf by an administrator.", "", "Click the link below to complete the onboarding process"]}, "user-account-status-changed": {"title": "User Account Status Changed", "buttonText": "Navigate to {{tenantName}}", "text": ["The status of your account has been modified by an administrator.", "New status: {{accountStatus}}"]}, "unknown-user-badged": {"title": "Unknown User Badged", "buttonText": "Navigate to {{tenantName}}", "text": ["An unknown user has just badged on {{chargeBoxID}} with the RFID Card {{badgeID}}."]}, "session-started": {"title": "Session Started", "buttonText": "View Session", "text": ["Your vehicle has been connected to the charging station {{chargeBoxID}} on connector {{connectorId}}."]}, "verification-email": {"title": "User Account Activation", "buttonText": "Activate your Account", "text": ["You have initiated a request to activate your account.", "", "Click on the link below to complete the activation."]}, "verification-email-user-import": {"title": "User Account Created", "buttonText": "Activate your Account", "text": ["An account has just been created on your behalf,", "", "Click on the link below to complete the activation."]}, "ocpi-patch-status-error": {"title": "Failed to transfer the Charging Station Statuses ", "buttonText": "Navigate to {{tenantName}}", "text": ["The OCPI transfer of the charging station statuses has failed on location {{location}}.", "", "Check the logs."]}, "oicp-patch-status-error": {"title": "Hubject - Send Statuses", "buttonText": "Navigate to {{tenantName}}", "text": ["The transfer to the Hubject roaming platform of the charging station statuses has failed in organization '{{tenantName}}'.", "", "Check the logs."]}, "oicp-patch-evses-error": {"title": "Hubject - Send Charging Stations", "buttonText": "Navigate to {{tenantName}}", "text": ["The transfer to the Hubject roaming platform of the charging station data has failed in organization '{{tenantName}}'.", "", "Check the logs."]}, "user-account-inactivity": {"title": "User Account Inactivity", "buttonText": "Navigate to {{tenantName}}", "text": ["You haven't logged in to the e-Mobility application since {{lastLogin}}.", "Be aware that in order to comply with the general data protection regulation, your account might be deleted by an administrator.", "", "Please log in and accept the latest end-user license agreement to prevent your account deletion."]}, "session-not-started": {"title": "No Session Started", "buttonText": "View Charging Station", "text": ["You haven't started the session on charging station {{chargeBoxID}}, connector {{connectorId}}."]}, "session-not-started-after-authorize": {"title": "No Session Started", "buttonText": "View Charging Station", "text": ["You badged on the charging station {{chargeBoxID}} but no session has been started."]}, "billing-user-synchronization-failed": {"title": "Billing - User Synchronization Failed", "buttonText": "Navigate to {{tenantName}}", "text": ["Unable to synchronize {{nbrUsersInError}} user(s) with the billing service provider."]}, "billing-invoice-synchronization-failed": {"title": "Billing - Invoice Synchronization Failed", "buttonText": "Navigate to {{tenantName}}", "text": ["Unable to synchronize {{nbrInvoicesInError}} invoice(s) with the billing service provider."]}, "billing-periodic-operation-failed": {"title": "Billing - Periodic Operation Failed", "buttonText": "Navigate to {{tenantName}}", "text": ["Unable to process {{nbrInvoicesInError}} invoice(s)."]}, "billing-account-activated": {"title": "Billing Account Activated", "buttonText": "Navigate to {{tenantName}}", "text": ["Congratulation!", "", "The onboarding process has been successfully completed and your billing account has been activated."]}, "car-synchronization-failed": {"title": "Car Synchronization Failed", "buttonText": "Navigate to {{tenantName}}", "text": ["Unable to synchronize {{nbrCarsInError}} car(s) with the electric vehicles data provider."]}, "compute-and-apply-charging-profiles-failed": {"title": "Failed to apply Charging Profiles", "buttonText": "Navigate to {{tenantName}}", "text": ["The attempt to apply charging profiles to the charging station {{chargeBoxID}} failed.", "The charging station was excluded from this smart charging run automatically."]}, "account-verification-notification-active": {"title": "Account Verified", "buttonText": "Navigate to {{tenantName}}", "text": ["Your account has been verified and activated with success!"]}, "account-verification-notification-inactive": {"title": "Account Verified", "buttonText": "Navigate to {{tenantName}}", "text": ["Your email has been successfully verified.", "", "An administrator will soon check and activate your account."]}, "admin-account-verification-notification": {"title": "User Account Verification", "buttonText": "Verify Account", "text": ["A new user with email {{email}} has just created an account.", "", "Please follow the link below to activate it."]}, "user-create-password": {"title": "User Account Created", "buttonText": "Set your Password", "text": ["An account has just been created on your behalf in '{{tenantName}}' organization.", "You will have to log in at least once to be able to charge your car.", "", "Click on the link below to set your password."]}}}