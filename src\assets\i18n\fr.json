{"billing": {"generatedUser": "Utilisateur g<PERSON> pour '{{email}}'", "chargingAtSiteArea": "Session : {{sessionID}} - Date : {{startDate}} à {startTime} - Energie Consommée : {{totalConsumption}} kWh sur la zone {{siteAreaName}}", "chargingAtChargeBox": "Session : {{sessionID}} - Date : {{startDate}} à {{startTime}} - Energie Consommée : {{totalConsumption}} kWh à la borne {{chargeBoxID}}", "header-itemDescription": "Session: {{sessionID}} - Date : {{startDate}} à {{startTime}} à la borne {{chargeBoxID}}", "flatFee-shortItemDescription": "Frais fixes", "energy-shortItemDescription": "Energie Consommée : {{quantity}} kWh", "chargingTime-shortItemDescription": "Du<PERSON><PERSON> de la charge : {{duration}}", "parkingTime-shortItemDescription": "Du<PERSON>e de stationnement: {{duration}}", "transfer-feeItemDescription": "Frais facturés pour {{nbSessions}} session(s)"}, "chargers": {"chargeBoxSN": "Numéro de série de la borne", "chargePointSN": "Numéro de série point de charge", "chargingStation": "<PERSON><PERSON>", "connector": "Connecteur", "firmwareVersion": "Version du firmware", "lastReboot": "<PERSON>nier redémarrage", "lastSeen": "Dernière connexion", "maxPower": "Puissance maximum (Watt)", "model": "<PERSON><PERSON><PERSON><PERSON>", "numberOfConnectors": "Nombre de connecteurs", "ocppProtocol": "Protocol OCPP", "ocppVersion": "Version OCPP", "powerLimitUnit": "Unité de limite de puissance", "timezone": "<PERSON><PERSON> horaire de <PERSON>", "vendor": "<PERSON><PERSON><PERSON>"}, "general": {"changedBy": "Changé par", "changedOn": "<PERSON><PERSON>", "createdOn": "<PERSON><PERSON><PERSON>", "date": "Date", "endDate": "Date de fin", "endTime": "Heure de fin", "invalidDate": "Date invalide", "invalidTime": "Heure invalide", "latitude": "Latitude", "longitude": "Longitude", "month": "<PERSON><PERSON>", "name": "Nom", "price": "Prix", "priceUnit": "Unité de prix", "site": "Site", "siteArea": "Zone", "startDate": "Date de début", "startTime": "<PERSON><PERSON> d<PERSON>", "time": "<PERSON><PERSON>", "value": "<PERSON><PERSON>", "year": "<PERSON><PERSON>"}, "loggings": {"action": "Action", "host": "<PERSON><PERSON><PERSON>", "level": "Niveau", "message": "Message", "method": "Méthode", "module": "<PERSON><PERSON><PERSON>", "process": "Processus", "source": "Source", "type": "Type"}, "notifications": {"sessionNotStarted": {"title": "Session non démarrée", "body": "Vous avez badgé sur la borne '{{chargeBoxID}}', mais aucune session n'a été démarrée"}, "sessionStarted": {"title": "Session d<PERSON><PERSON><PERSON>e", "body": "Votre session sur la borne '{{chargeBoxID}}', connecteur '{{connectorId}}', a été démarrée avec succès dans l'organisation '{{tenantName}}'"}, "ocpiPatchChargingStationsStatusesError": {"title": "Itinérance - transmission de l'état des bornes", "body": "La transmission de l'état des bornes vers la plateforme d'itinérance a échoué pour le site '{{location}}' de l'organisation '{{tenantName}}', vérifier les logs"}, "oicpPatchChargingStationsStatusesError": {"title": "Hubject - transmission de l'état des bornes", "body": "La transmission de l'état des bornes vers la plateforme Hubject a échoué pour le site '{{location}}' de l'organisation '{{tenantName}}', vérifier les logs"}, "oicpPatchChargingStationsError": {"title": "Hubject - transmission des informations relatives aux bornes", "body": "La transmission des données des bornes de l'organisation '{{tenantName}}' vers la plateforme Hubject a échoué, vérifier les logs"}, "optimalChargeReached": {"title": "Charge optimale atteinte", "body": "Votre véhicule, connecté sur '{{chargeBoxID}}', connecteur '{{connectorId}}', a atteint la charge optimale de 85%'"}, "endOfCharge": {"title": "Charge terminée", "body": "Votre véhicule, connecté sur la borne '{{chargeBoxID}}', connecteur '{{connectorId}}', a terminé sa session de recharge'"}, "endOfSession": {"title": "Session de recharge terminée", "body": "Votre session de recharge sur la borne '{{chargeBoxID}}', connecteur '{{connectorId}}' est terminée'"}, "chargingStationStatusError": {"title": "Borne en erreur", "body": "Une erreur est survenue sur '{{chargeBoxID}}', connecteur '{{connectorId}}' dans l'organisation '{{tenantName}}': {{error}}"}, "unknownUserBadged": {"title": "Utilisateur inconnu", "body": "Un utilisateur inconnu vient de badger sur '{{chargeBoxID}}' avec le badge ID '{{badgeID}}' dans l'organisation '{{tenantName}}'"}, "chargingStationRegistered": {"title": "Connexion borne", "body": "La borne '{{chargeBoxID}}' vient de se connecter au serveur central dans l'organisation '{{tenantName}}'"}, "userAccountStatusChanged": {"title": "Compte {{status}}", "activated": "activé", "suspended": "suspendu", "body": "Votre compte a été {{status}} par un administrateur dans l'organisation '{{tenantName}}'"}, "userAccountInactivity": {"title": "Compte utilisateur", "body": "Votre compte est inactif depuis le {{lastLogin}} dans l'organisation '{{tenantName}}'. Pour des raisons légales, les comptes inactifs depuis plus de 6 mois doivent être supprimés"}, "preparingSessionNotStarted": {"title": "Session non démarrée", "body": "Session non démarrée sur la borne '{{chargeBoxID}}', connecteur '{{connectorId}}' dans l'organisation '{{tenantName}}'"}, "offlineChargingStation": {"title": "<PERSON><PERSON> hors ligne", "body": "Les bornes suivantes sont hors ligne dans l'organisation '{{tenantName}}': {{chargeBoxIDs}}"}, "billingUserSynchronizationFailed": {"title": "Synchro utilisateurs", "body": "Impossible de synchroniser {{nbrUsersInError}} utilisateur(s) avec le service de facturation dans l'organisation '{{tenantName}}'"}, "billingInvoiceSynchronizationFailed": {"title": "Synchro factures", "body": "Impossible de synchroniser {{nbrInvoicesInError}} facture(s) avec le service de facturation dans l'organisation '{{tenantName}}'"}, "billingPeriodicOperationFailed": {"title": "Opération de facturation périodique", "body": "Le traitement de {{nbrInvoicesInError}} facture(s) a échoué dans l'organisation '{{tenantName}}'"}, "computeAndApplyChargingProfilesFailed": {"title": "Profils de charge", "body": "Impossible d'appliquer les profils de charge sur '{{chargeBoxID}}' de la zone '{{siteAreaName}}' dans l'organization '{{tenantName}}'"}, "billingNewInvoicePaid": {"title": "Nouvelle facture payée", "body": "Une nouvelle facture ({{invoiceNumber}}) d'un montant de {{amount}} a été payée et est maintenant disponible"}, "billingNewInvoiceOpen": {"title": "Nouvelle facture a payer", "body": "Une nouvelle facture ({{invoiceNumber}}) d'un montant de {{amount}} est disponible, vérifiez vos emails pour finaliser le paiement"}, "billingAccountCreationLink": {"title": "Connection d'un compte de facturation", "body": "Vous avez été invité à créer un compte de facturation. Veuillez vérifier vos emails pour terminer le processus"}, "billingAccountActivated": {"title": "Compte de facturation activé", "body": "Votre compte de facturation a été activé"}, "endUserErrorNotification": {"title": "Un utitilisateur a reporté une erreur", "body": "'{{userName}}' a reporté: '{{errorTitle}}' - '{{errorDescription}}' dans l'organisation '{{tenantName}}'"}, "accountVerificationNotification": {"title": "Compte vérifié", "bodyVerified": "Votre compte a été vérifié avec succès. Un administrateur va examiner et activer votre compte.", "bodyVerifiedAndActivated": "Votre compte a été vérifié et activé avec succès."}}, "siteArea": "Zone", "statistics": {"consumption": "Consommation (kW.h)", "inactivity": "Inactivité (Heures)", "numberOfSessions": "Nombre de sessions", "usage": "Utilisation (Heures)"}, "tags": {"id": "ID du badge", "description": "Description du badge", "virtualBadge": "<PERSON>ge virtuel"}, "transactions": {"totalConsumption": "Consommation totale (kW.h)", "totalDuration": "<PERSON><PERSON><PERSON> totale (Mins)", "totalInactivity": "Inactivité totale (Mins)"}, "users": {"email": "Email", "eulaAcceptedOn": "CGU acceptées le", "firstName": "Prénom", "id": "Identifiant", "role": "R<PERSON><PERSON>", "status": "Statut", "user": "Utilisa<PERSON>ur", "userID": "Identifiant utilisateur"}, "email": {"greetings": "Bonjour {{recipientName}}", "footerText": "Cet e-mail est destiné à {{recipientEmail}}. Il vous a été transmis via un service automatique. Merci de ne pas répondre. Vous pouvez vous désinscrire en modifiant vos paramètres de notification.", "new-registered-user": {"title": "Compte utilisateur c<PERSON>", "buttonText": "Activer votre compte", "text": ["Votre compte a été créé avec succès.", "", "Veuillez suivre le lien ci-dessous pour l'activer."]}, "request-password": {"title": "Demande de réinitialisation du mot de passe", "buttonText": "Réinitialiser le mot de passe", "text": ["Vous venez de demander la réinitialisation de votre mot de passe", "", "Veuillez suivre le lien ci-dessous pour en définir un nouveau."]}, "optimal-charge-reached": {"title": "Charge optimale atteinte", "buttonText": "Voir la session", "text": ["<PERSON><PERSON><PERSON> vé<PERSON>ule, connecté sur le connecteur {{connectorId}} de la borne {{chargeBoxID}}, a atteint un niveau de charge optimale."], "table": [{"label": "Session", "value": "{{transactionId}}"}, {"label": "Niveau de la batterie", "value": "{{stateOfCharge}} %"}]}, "end-of-session": {"title": "Session terminée", "buttonText": "Voir la session", "text": ["La session démarrée sur le connecteur {{connectorId}} de la borne {{chargeBoxID}} vient de se terminer."], "table": [{"label": "Session", "value": "{{transactionId}}"}, {"label": "Consommation", "value": "{{totalConsumption}} kW.h"}, {"label": "Durée totale", "value": "{{totalDuration}}"}, {"label": "Temps d'inactivité", "value": "{{totalInactivity}}"}, {"label": "Niveau de la batterie", "value": "{{stateOfCharge}} %"}]}, "end-of-signed-session": {"title": "Session Finished - Signed Session", "buttonText": "View Session", "text": ["Ladetransaktion abgeschlossen"], "table": [{"label": "Charger", "value": "{{chargeBoxID}}"}, {"label": "Connector", "value": "{{connectorId}}"}, {"label": "RFID Card", "value": "{{tagId}}"}, {"label": "Start time", "value": "{{startDate}}"}, {"label": "End time", "value": "{{endDate}}"}, {"label": "Start counter", "value": "{{meterStart}} kWh"}, {"label": "Stop counter", "value": "{{meterStop}} kWh"}, {"label": "Consumption", "value": "{{totalConsumption}} kWh"}, {"label": "Cost", "value": "{{price}}€ {{relativeCost}}€/kWh) (informative)"}, {"label": "Contract for", "value": "LOCAL (informative)"}]}, "end-of-charge": {"title": "Charge terminée", "buttonText": "Voir la session", "text": ["Votre véhicule connecté sur le connecteur {{connectorId}} de la borne {{chargeBoxID}} vient de finir de charger."], "table": [{"label": "Session", "value": "{{transactionId}}"}, {"label": "Consommation", "value": "{{totalConsumption}} kW.h"}, {"label": "Durée totale", "value": "{{totalDuration}}"}, {"label": "Niveau de la batterie", "value": "{{stateOfCharge}} %"}]}, "billing-new-invoice-paid": {"title": "Facturation - Facture payée", "buttonText": "Télécharger la facture", "text": ["La facture numéro {{invoiceNumber}} a été payée et le document correspondant peut maintenant être téléchargé."], "table": [{"label": "<PERSON><PERSON> payé", "value": "{{invoiceAmount}}"}]}, "billing-new-invoice-unpaid": {"title": "Facturation - Nouvelle facture émise", "buttonText": "Payer la facture", "text": ["La facture numéro {{invoiceNumber}} a été émise et le document correspondant peut maintenant être téléchargé.", "", "Veuillez suivre le lien ci-dessous pour finaliser le paiement."], "table": [{"label": "<PERSON><PERSON> dû", "value": "{{invoiceAmount}}"}]}, "charging-station-registered": {"title": "<PERSON><PERSON>", "buttonText": "Voir la borne", "text": ["La borne {{chargeBoxID}} vient de se connecter au serveur central."]}, "end-user-error-notification": {"title": "Erreur signalée par un utilisateur", "buttonText": "Naviguer vers {{tenantName}}", "text": ["L'utilisateur {{name}} a reporté l'erreur suivante :", "", "Erreur : {{errorTitle}}", "Description : {{errorDescription}}", "", "E-mail : {{email}}", "Téléphone : {{phone}}"]}, "offline-charging-station": {"title": "Bornes déconnect<PERSON>", "buttonText": "Naviguer vers {{tenantName}}", "text": ["Votre organisation a {{nbChargingStationIDs}} borne(s) qui se trouvent dans un état déconnecté.", "", "Bornes: {{tenFirstChargingStationIDs}}"]}, "charging-station-status-error": {"title": "Erreur sur {{chargeBoxID}}, connecteur {{connectorId}}", "buttonText": "Voir l'erreur", "text": ["Une erreur est survenue sur la borne {{chargeBoxID}}, connecteur {{connectorId}}.", "Erreur: {{error}}"]}, "billing-account-created": {"title": "Embarquement d'un compte de facturation", "buttonText": "<PERSON><PERSON><PERSON><PERSON>", "text": ["Un compte de facturation a été créé en votre nom par un administrateur.", "", "Veuillez suivre le lien ci-dessous pour finaliser la procédure d'embarquement"]}, "user-account-status-changed": {"title": "Compte utilisateur - Statut modifié", "buttonText": "Naviguer vers {{tenantName}}", "text": ["Le statut de votre compte a été modifié par un administrateur.", "Nouveau statut : {{accountStatus}}"]}, "unknown-user-badged": {"title": "Un utilisateur inconnu a tenté de badger", "buttonText": "Naviguer vers {{tenantName}}", "text": ["Un utilisateur a tenté de badger sur la borne {{chargeBoxID}} avec le badge {{badgeID}}."]}, "session-started": {"title": "Session d<PERSON><PERSON><PERSON>e", "buttonText": "Voir la session", "text": ["Votre véhicule est connecté sur le connecteur {{connectorId}} de la borne {{chargeBoxID}}."]}, "verification-email": {"title": "Activation de votre compte utilisateur", "buttonText": "<PERSON><PERSON> le compte", "text": ["Vous avez demandé l'activation de votre compte.", "", "Veuillez suivre le lien ci-dessous pour finaliser la procédure."]}, "verification-email-user-import": {"title": "Compte utilisateur c<PERSON>", "buttonText": "<PERSON><PERSON> le compte", "text": ["Un compte utilisateur a été créé en votre nom.", "", "Veuillez suivre le lien ci-dessous pour l'activer."]}, "ocpi-patch-status-error": {"title": "Echec de la transmission de l'état des bornes", "buttonText": "Naviguer vers {{tenantName}}", "text": ["La transmission de l'état des bornes du site {{location}} vers la plateforme d'ititinérance OCPI a échoué.", "", "Vérifiez les logs."]}, "oicp-patch-status-error": {"title": "Hubject - Echec de la transmission de l'état des bornes", "buttonText": "Naviguer vers {{tenantName}}", "text": ["La transmission de l'état des bornes vers la plateforme d'ititinérance Hubject a échoué.", "", "Vérifiez les logs."]}, "oicp-patch-evses-error": {"title": "Hubject - Echec de la transmission des bornes", "buttonText": "Naviguer vers {{tenantName}}", "text": ["La transmission de la liste des bornes vers la plateforme d'ititinérance Hubject a échoué.", "", "Vérifiez les logs."]}, "user-account-inactivity": {"title": "Compte utilisateur inactif", "buttonText": "Naviguer vers {{tenantName}}", "text": ["Vous ne vous êtes pas authentifié sur l'application e-Mobility depuis le {{lastLogin}}.", "Veuillez noter que pour des raisons légales de protection des données, votre compte est susceptible d'être supprimé par un administrateur.", "", "Authentifiez-vous et acceptez les dernières conditions d'utilisation afin d'éviter la suppression de votre compte."]}, "session-not-started": {"title": "Session non démarrée", "buttonText": "Voir la borne", "text": ["La session sur le connecteur {{connectorId}} de la borne {{chargeBoxID}} n'a pas démarré."]}, "session-not-started-after-authorize": {"title": "Session non démarrée", "buttonText": "Voir la borne", "text": ["Vous avez badgé sur la borne {{chargeBoxID}} mais aucune session n'a démarré."]}, "billing-user-synchronization-failed": {"title": "Facturation - Echec de la synchronisation des utilisateurs", "buttonText": "Naviguer vers {{tenantName}}", "text": ["La synchronisation des utilisateurs avec le système de facturation a échoué pour {{nbrUsersInError}} utilisateur(s)."]}, "billing-invoice-synchronization-failed": {"title": "Facturation - Echec de la synchronisation des factures", "buttonText": "Naviguer vers {{tenantName}}", "text": ["La synchronisation des factures avec le système de facturation a échoué pour {{nbrInvoicesInError}} document(s)."]}, "billing-periodic-operation-failed": {"title": "Facturation - Echec de la procédure de facturation périodique", "buttonText": "Naviguer vers {{tenantName}}", "text": ["Le traitement de {{nbrInvoicesInError}} facture(s) à échoué."]}, "billing-account-activated": {"title": "Compte de facturation activé", "buttonText": "Naviguer vers {{tenantName}}", "text": ["Félicitations", "", "La procédure d'embarquement de votre compte de facturation est terminée. Votre compte est maintenant actif."]}, "car-synchronization-failed": {"title": "Echec de la synchronisation des données de véhicules", "buttonText": "Naviguer vers {{tenantName}}", "text": ["La procédure de synchronisation des données des véhicules a échoué pour {{nbrCarsInError}} d'entre eux."]}, "compute-and-apply-charging-profiles-failed": {"title": "Echec de l'application des profils de charge", "buttonText": "Naviguer vers {{tenantName}}", "text": ["La tentative d'application des profils de charge sur la borne {{chargeBoxID}} a échoué.", "La borne a été automatiquement exclue de cette session de charge intelligente (Smart Charging)."]}, "account-verification-notification-active": {"title": "Compte utilisateur vérifié", "buttonText": "Naviguer vers {{tenantName}}", "text": ["Votre e-mail a été vérifié. Votre compte est maintenant actif."]}, "account-verification-notification-inactive": {"title": "Compte utilisateur vérifié", "buttonText": "Naviguer vers {{tenantName}}", "text": ["Votre e-mail a été vérifié.", "", "Votre compte va être activé par un administrateur. <PERSON><PERSON>i de patienter."]}, "admin-account-verification-notification": {"title": "Vérification d'un nouveau compte utilisateur", "buttonText": "Vérifier le compte", "text": ["Un nouvel utilisateur vient de créer un compte avec l'e-mail {{email}}.", "", "Veuillez suivre le lien ci-dessous pour finaliser son activation."]}, "user-create-password": {"title": "Compte utilisateur c<PERSON>", "buttonText": "Dénifir le mot de passe", "text": ["Un compte utilisateur vient d'être créé en votre nom dans l'organisation '{{tenantName}}'.", "Vous devez vous authentifier au moins une fois pour activer la possibilité de recharger votre véhicule.", "", "Veuillez suivre le lien ci-dessous pour définir votre mot de passe."]}}}