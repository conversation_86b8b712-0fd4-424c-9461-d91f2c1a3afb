{"name": "ev-server", "version": "2.7.5", "engines": {"node": "18.x.x", "npm": "10.x.x"}, "repository": {"type": "git", "url": "https://github.com/sap-labs-france/ev-server.git"}, "license": "Apache-2.0", "licenses": [{"type": "Apache-2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0"}], "description": "Backend for Electric Vehicle charging station", "author": "Open E-Mobility <<EMAIL>>", "homepage": "https://github.com/sap-labs-france/ev-server", "main": "dist/start.js", "scripts": {"git:sinit": "git submodule update --init --recursive --force", "git:sdiff": "git diff && git submodule foreach 'git diff'", "git:supdate": "git submodule update --remote --recursive --merge", "git:spush": "git push --recurse-submodules=on-demand", "git:sreset": "git submodule foreach 'git reset --hard'", "prestart": "npm run build:prod", "start": "npm version && npm run start:prod", "start:dev": "npm version && cross-env NODE_OPTIONS=\"--max-old-space-size=4096\" NODE_ENV=development-build node -r source-map-support/register dist/start.js", "start:dev:debug": "npm version && cross-env NODE_ENV=development ts-node-dev --inspect --files --max-old-space-size=4096 -- src/start.ts", "start:dev:debug:ocppj": "npm version && cross-env SERVER_ROLE=ocppj NODE_ENV=development ts-node-dev --inspect --files --max-old-space-size=4096 -- src/start.ts", "start:dev:debug:rest": "npm version && cross-env SERVER_ROLE=rest NODE_ENV=development ts-node-dev --inspect --files --max-old-space-size=4096 -- src/start.ts", "start:dev:debug:nodemon": "npm version && nodemon --exec \"ts-node --files\" src/start.ts 9229", "start:dev:nodemon": "npm version && nodemon --exec \"ts-node --files\" src/start.ts", "start:dev:prof": "npm version && cross-env NODE_OPTIONS=\"--max-old-space-size=4096\" NODE_ENV=development node -r source-map-support/register --prof -- dist/start.js", "start:dev:doctorprof": "npm version && cross-env NODE_OPTIONS=\"--max-old-space-size=4096\" NODE_ENV=development clinic doctor -- node -r source-map-support/register dist/start.js", "start:dev:flameprof": "npm version && cross-env NODE_OPTIONS=\"--max-old-space-size=4096\" NODE_ENV=development clinic flame -- node -r source-map-support/register dist/start.js", "start:dev:bubbleprof": "npm version && cross-env NODE_OPTIONS=\"--max-old-space-size=4096\" NODE_ENV=development clinic bubbleprof -- node -r source-map-support/register dist/start.js", "start:prod": "npm version && cross-env NODE_OPTIONS=\"--max-old-space-size=4096\" NODE_ENV=production node -r source-map-support/register dist/start.js", "start:prod:prof": "npm version && cross-env NODE_OPTIONS=\"--max-old-space-size=4096\" NODE_ENV=production node -r source-map-support/register --prof dist/start.js", "start:prod:doctorprof": "npm version && cross-env NODE_OPTIONS=\"--max-old-space-size=4096\" NODE_ENV=production clinic doctor -- node -r source-map-support/register dist/start.js", "start:prod:flameprof": "npm version && cross-env NODE_OPTIONS=\"--max-old-space-size=4096\" NODE_ENV=production clinic flame -- node -r source-map-support/register dist/start.js", "start:prod:bubbleprof": "npm version && cross-env NODE_OPTIONS=\"--max-old-space-size=4096\" NODE_ENV=production clinic bubbleprof -- node -r source-map-support/register dist/start.js", "start:email": "maildev --web 1080 --web-ip 127.0.0.1 --smtp 1025 --verbose --incoming-user evse-mail-user --incoming-pass evse-mail-pwd", "start:emailBackup": "maildev --web 2080 --web-ip 127.0.0.1 --smtp 2025 --verbose --incoming-user evse-mail-user --incoming-pass evse-mail-pwd", "build": "npm version && npm run build:prod", "build:dev": "npm version && webpack --env dev", "build:prod": "npm version && webpack --env prod", "clean-install": "npm version && rimraf package-lock.json && rimraf node_modules && npm cache clear --force && npm install", "eslint": "cross-env TIMING=1 eslint --ext .js,.ts types src test", "eslint-html": "cross-env TIMING=1 eslint --ext .js,.ts types src test -f=node_modules/eslint-html-reporter/reporter-team-city.js -o test/results-eslint/eslint-results.html", "eslint:fix": "cross-env TIMING=1 eslint --ext .js,.ts --fix types src test", "autocannon": "autocannon", "coverage": "nyc report --reporter=lcov", "coverage:html": "nyc report --reporter=html", "clinic:clean": "clinic clean", "npm-check": "npm-check", "check:i18n": "cross-env TS_NODE_FILES=true ts-node-dev --files test/I18nChecker.ts", "import-sort": "npx import-sort-cli --write 'src/**/*.ts{,x}' 'test/**/*.ts{,x}' 'types/**/*.ts{,x}'", "test-jest-pluggin": "npm version && cross-env TS_NODE_FILES=true jest --config='test/jest.config.ts' --reporters='default' --reporters='./JestEvseReporter.js' --runInBand --logHeapUsage --verbose --colors --silent --forceExit --testPathIgnorePatterns='.*Runner.ts$'", "test": "npm run test:jest -- test/api/*Test.ts", "test:createContext": "npm run test:jest -- test/api/context/ContextBuilderRunner.ts", "test:create:utbilling": "cross-env TS_NODE_FILES=true TENANT_FILTER=utbilling.* jest --config='test/jest.config.ts' --runInBand --logHeapUsage --verbose --colors --silent --forceExit -- test/**/**/ContextBuilderRunner.ts", "test:asset": "npm run test:jest -- test/api/AssetTest.ts", "test:authentication": "npm run test:jest -- test/api/AuthenticationTest.ts", "test:billing": "npm run test:jest -- test/api/BillingTest.ts", "test:bp": "npm run test:jest -- test/api/BillingPlatformTest.ts", "test:car": "npm run test:jest -- test/api/CarTest.ts", "test:componentActivation": "npm run test:jest -- test/api/ComponentActivationTenantTest.ts", "test:crypto": "npm run test:jest -- test/api/EncryptionSettingTest.ts", "test:filter": "npm run test:jest -- test/api/FilterTest.ts", "test:firmwareUpdateStatus": "npm run test:jest -- test/api/FirmwareUpdateStatusTest.ts", "test:locking": "npm run test:jest -- test/api/LockingTest.ts", "test:ocpi": "npm run test:jest -- test/api/OCPIServerTest.ts", "test:ocpp": "npm run test:jest -- test/api/OCPP*Test.ts", "test:oicp": "npm run test:jest -- test/api/OICP*Test.ts", "test:registrationToken": "npm run test:jest -- test/api/RegistrationTokenTest.ts", "test:security": "npm run test:jest -- test/api/SecurityTest.ts", "test:setting": "npm run test:jest -- test/api/SettingTest.ts", "test:org": "npm run test:jest -- test/api/*OrgTest.ts", "test:company": "npm run test:jest -- test/api/CompanyOrg*Test.ts", "test:site": "npm run test:jest -- test/api/SiteOrg*Test.ts", "test:siteArea": "npm run test:jest -- test/api/SiteAreaOrg*Test.ts", "test:smartCharging": "npm run test:jest -- test/api/SmartChargingTest.ts", "test:stat": "npm run test:jest -- test/api/StatisticsTest.ts", "test:tag": "npm run test:jest -- test/api/TagTest.ts", "test:tenant": "npm run test:jest -- test/api/*TenantTest.ts", "test:transaction": "npm run test:jest -- test/api/TransactionTest.ts", "test:template": "npm run test:jest -- test/api/ChargingStationTemplateTest.ts", "test:ut-template": "npm run test:jest -- test/api/UnitTestTemplate.ts", "test:user": "npm run test:jest -- test/api/UserTest.ts", "test:email": "npm run test:jest -- test/api/EmailTest.ts", "test:jest": "npm version && cross-env NODE_OPTIONS=\"--max-old-space-size=4096\" TS_NODE_FILES=true jest --config='./test/jest.config.ts' --runInBand --logHeapUsage --verbose --colors --silent --forceExit"}, "dependencies": {"ajv": "^8.11.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.1.0", "api": "^6.1.1", "async": "^3.2.5", "axios": "^0.27.2", "axios-retry": "^3.3.1", "basic-auth": "^2.0.1", "bcryptjs": "^2.4.3", "bluebird": "^3.7.2", "body-parser": "^1.20.0", "body-parser-xml": "^2.0.3", "busboy": "^1.6.0", "chalk": "4.1.2", "cors": "^2.8.5", "countries-list": "^2.6.1", "csvtojson": "^2.0.10", "curvereduce": "^0.0.4", "decimal.js": "^10.4.2", "dotenv": "^16.3.1", "emailjs": "github:ramancero/emailjs", "express": "^4.18.2", "express-sanitizer": "^1.0.6", "express-useragent": "^1.0.15", "firebase-admin": "^11.2.0", "helmet": "^5.1.1", "hpp": "^0.2.3", "html-pdf": "^3.0.1", "http-status-codes": "^2.2.0", "https": "^1.0.0", "i18n-iso-countries": "^7.5.0", "i18n-js": "^3.9.2", "intl": "^1.2.5", "jimp": "^0.16.2", "JSONStream": "^1.3.5", "jsonwebtoken": "^8.5.1", "locale": "^0.1.0", "lodash": "^4.17.21", "mjml": "^4.13.0", "moment": "^2.29.4", "moment-duration-format": "^2.3.2", "moment-timezone": "^0.5.38", "mongo-sanitize": "^1.1.0", "mongo-uri-builder": "^4.0.0", "mongodb": "^4.11.0", "morgan": "^1.10.0", "mustache": "^4.2.0", "nanoid": "3.3.4", "node-cron": "^3.0.2", "object-sizeof": "^1.6.3", "otplib": "^12.0.1", "passport": "^0.6.0", "passport-jwt": "^4.0.0", "password-generator": "^2.3.2", "pdf": "^0.1.0", "pdfkit": "^0.13.0", "phantomjs-prebuilt": "^2.1.13", "prom-client": "^14.1.0", "puppeteer": "^21.11.0", "puppeteer-core": "^23.4.0", "qrcode": "^1.5.1", "razorpay": "^2.9.2", "request": "^2.88.2", "rfc2047": "^4.0.1", "role-acl": "^4.5.4", "simple-odata-server": "^1.1.2", "source-map-support": "^0.5.21", "stripe": "^9.16.0", "strong-soap": "^3.4.0", "swagger-ui-express": "^4.5.0", "tslib": "^2.4.0", "twilio": "^4.14.1", "tz-lookup": "^6.1.25", "urlencode": "^1.1.0", "uWebSockets.js": "github:uNetworking/uWebSockets.js#v20.10.0", "validator": "^13.7.0", "ws": "^8.10.0", "xml": "^1.0.1"}, "optionalDependencies": {"bufferutil": "^4.0.7", "utf-8-validate": "^5.0.10"}, "devDependencies": {"@faker-js/faker": "^7.6.0", "@istanbuljs/nyc-config-typescript": "^1.0.2", "@types/basic-auth": "^1.1.3", "@types/bcryptjs": "^2.4.2", "@types/bluebird": "^3.5.37", "@types/bluebird-global": "^3.5.13", "@types/body-parser": "^1.19.2", "@types/body-parser-xml": "^2.0.2", "@types/busboy": "^1.5.0", "@types/chai": "^4.3.3", "@types/chai-datetime": "0.0.37", "@types/chai-subset": "^1.3.3", "@types/chalk": "^2.2.0", "@types/convict": "^6.1.1", "@types/cors": "^2.8.12", "@types/ejs": "^3.1.1", "@types/express": "^4.17.14", "@types/express-useragent": "^1.0.2", "@types/hpp": "^0.2.2", "@types/i18n-js": "^3.8.3", "@types/intl": "^1.2.0", "@types/jest": "^29.2.0", "@types/jsonstream": "^0.8.30", "@types/jsonwebtoken": "^8.5.9", "@types/lodash": "^4.14.186", "@types/moment-duration-format": "^2.2.3", "@types/moment-timezone": "^0.5.30", "@types/mongo-sanitize": "^1.0.1", "@types/mustache": "^4.2.1", "@types/node-cron": "^3.0.4", "@types/passport": "^1.0.11", "@types/passport-jwt": "^3.0.7", "@types/pdfkit": "^0.12.7", "@types/rfc2047": "^2.0.1", "@types/rosie": "0.0.40", "@types/tz-lookup": "^6.1.0", "@types/urlencode": "^1.1.2", "@types/uuid": "^8.3.4", "@types/validator": "^13.7.9", "@types/ws": "^8.5.3", "@typescript-eslint/eslint-plugin": "^5.41.0", "@typescript-eslint/parser": "^5.41.0", "autocannon": "^7.10.0", "chai": "^4.3.6", "chai-datetime": "^1.8.0", "chai-subset": "^1.6.0", "circular-dependency-plugin": "^5.2.2", "clean-webpack-plugin": "^4.0.0", "clinic": "^12.0.0", "convict": "^6.2.3", "copy-webpack-plugin": "^11.0.0", "cross-env": "^7.0.3", "deep-object-diff": "^1.1.7", "eslint": "^8.26.0", "eslint-html-reporter": "^0.7.4", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsdoc": "^39.3.25", "jest": "^29.2.2", "jest-circus": "^29.2.2", "jest-html-reporter": "^3.7.0", "jest-stare": "^2.4.1", "json-schema-faker": "^0.5.0-rcv.44", "maildev": "^2.0.5", "ncp": "^2.0.0", "nodemon": "^2.0.20", "npm-check": "^6.0.1", "nyc": "^15.1.0", "querystring": "^0.2.1", "rimraf": "^3.0.2", "rosie": "^2.1.0", "ts-jest": "^29.0.3", "ts-loader": "^9.4.1", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "typescript": "^4.8.4", "webpack": "^5.74.0", "webpack-cli": "^4.10.0", "webpack-merge": "^5.8.0", "webpack-node-externals": "^3.0.0", "webpack-shell-plugin-next": "^2.2.2", "xml": "^1.0.1", "xml2json": "^0.12.0"}}